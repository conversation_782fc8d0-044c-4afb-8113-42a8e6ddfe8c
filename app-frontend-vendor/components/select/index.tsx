import * as Select from "@radix-ui/react-select";
import styled from "@emotion/styled";
import { propcolors } from "../../styles/colors";

export const SelectRoot = styled(Select.Root)``;
export const SelectTrigger = styled(Select.Trigger)`
  display: inline-flex;
  align-items: center;
  justify-content: space-between;
  border-radius: 4px;
  padding: 0 15px;
  font-size: 13px;
  line-height: 1;
  height: 35px;
  gap: 5px;
  background-color: white;
  color: ${propcolors.red[700]};
  &:hover {
    background-color: ${propcolors.gray[200]};
  }
  &:focus {
    box-shadow: 0 0 0 1px ${propcolors.red[700]};
  }
  &[data-placeholder] {
    color: var(--violet9);
  }
`;
export const SelectValue = styled(Select.Value)``;
export const SelectIcon = styled(Select.Icon)``;
export const SelectPortal = styled(Select.Portal)``;
export const SelectContent = styled(Select.Content)`
  overflow: hidden;
  background-color: white;
  border-radius: 6px;
  box-shadow: 0px 10px 38px -10px rgba(22, 23, 24, 0.35),
    0px 10px 20px -15px rgba(22, 23, 24, 0.2);
`;
export const SelectViewport = styled(Select.Viewport)`
  padding: 5px;
`;
export const SelectItem = styled(Select.Item)`
  font-size: 14px;
  line-height: 1;
  color: ${propcolors.red[700]};
  border-radius: 3px;
  display: flex;
  align-items: center;
  padding: 8px 12px 8px 24px;
  position: relative;
  user-select: none;
  &[data-disabled] {
    color: gray;
    pointer-events: none;
  }
  &[data-highlighted] {
    outline: none;
    background-color: ${propcolors.red[700]};
    color: ${propcolors.white};
  }
`;
export const SelectItemText = styled(Select.ItemText)``;
export const SelectItemIndicator = styled(Select.ItemIndicator)`
  position: absolute;
  left: 0;
  width: 25px;
  display: inline-flex;
  align-items: center;
  justify-content: center;
`;
export const SelectScrollUpBtn = styled(Select.ScrollUpButton)``;
export const SelectScrollDnBtn = styled(Select.ScrollDownButton)``;
export const SelectGroup = styled(Select.Group)``;
export const SelectLabel = styled(Select.Label)``;
export const SelectSeparator = styled(Select.Separator)``;
