import styled from "@emotion/styled";
import { MobileListItem } from "./components/item";


type MobileListProps = {
  className?: string;
  data: Lead[] | Partner[] | undefined | null;
}

const Presentational: React.FC<MobileListProps> = ({ className, data }) => {
  return (
    <div className={className}>
      {data && data.map((item, index) => (
        <MobileListItem key={index} data={item} />
      ))}
    </div>
  )
};

const Styled = styled(Presentational)`
  display: grid;
  gap: 0.5rem;
  grid-auto-rows: min-content;
`;

export const MobileList: React.FC<MobileListProps> = ({ data }) => {
  return (
    <Styled data={data} />
  );
}