/** @type {import('next').NextConfig} */
const allowedHostnames = (process.env.ALLOWED_IMAGE_HOST_NAMES || "")
  .split(",")
  .filter(Boolean);
const remotePatterns = allowedHostnames.map((hostname) => ({
  protocol: process.env.ALLOWED_IMAGE_HOST_PROTOCOL || "https",
  hostname: hostname.trim(),
  port: process.env.ALLOWED_IMAGE_HOST_PORT || "",
}));
const isMaintenance = process.env.NEXT_PUBLIC_MAINTENANCE_MODE === "true";

const nextConfig = {
  async redirects() {
    if (!isMaintenance) {
      return [
        {
          source: "/settings",
          destination: "/settings/company",
          permanent: true,
        },
      ];
    }
    return [
      {
        source: "/:path((?!maintenance).*)*",
        destination: "/maintenance",
        permanent: false,
      },
    ];
  },
  // anyflowのCORS対策
  async rewrites() {
    return [
      {
        source: "/api/sdk/token",
        destination: "https://for-product-api.anyflow.jp/sdk/token",
        basePath: false,
      },
    ];
  },
  reactStrictMode: true,
  swcMinify: true,
  webpack: (config) => {
    config.module.rules.push({
      test: /\.svg$/,
      use: [
        {
          loader: "@svgr/webpack",
          options: {
            svgo: false, // 圧縮無効
          },
        },
      ],
    });
    config.module.rules.push({
      test: /\.node/,
      use: "raw-loader",
    });
    return config;
  },
  images: {
    disableStaticImages: true, // importした画像の型定義設定を無効にする,
    domains: ["s3.ap-northeast-1.amazonaws.com", "test-prop-files.s3.ap-northeast-1.amazonaws.com", "prod-prop-files.s3.ap-northeast-1.amazonaws.com", "localhost", "test-prop-contact-files.s3.ap-northeast-1.amazonaws.com", "prod-prop-contact-files.s3.ap-northeast-1.amazonaws.com"],
    remotePatterns:
      remotePatterns.length > 0
        ? remotePatterns
        : [
            {
              protocol: "https",
              hostname: "*.s3.ap-northeast-1.amazonaws.com",
              port: "",
            },
          ],
    unoptimized: process.env.IMAGE_UNOPTIMIZED === "true", // 無設定状態ではデフォルトで最適化は有効とする
  },
};

module.exports = nextConfig;

// Injected content via Sentry wizard below

const { withSentryConfig } = require("@sentry/nextjs");

module.exports = withSentryConfig(module.exports, {
  // For all available options, see:
  // https://github.com/getsentry/sentry-webpack-plugin#options

  org: "1a033bda2796",
  project: "app-frontend-vendor",

  // Only print logs for uploading source maps in CI
  silent: !process.env.CI,

  // For all available options, see:
  // https://docs.sentry.io/platforms/javascript/guides/nextjs/manual-setup/

  // Upload a larger set of source maps for prettier stack traces (increases build time)
  widenClientFileUpload: true,

  // Uncomment to route browser requests to Sentry through a Next.js rewrite to circumvent ad-blockers.
  // This can increase your server load as well as your hosting bill.
  // Note: Check that the configured route will not match with your Next.js middleware, otherwise reporting of client-
  // side errors will fail.
  // tunnelRoute: "/monitoring",

  // Hides source maps from generated client bundles
  hideSourceMaps: true,

  // Automatically tree-shake Sentry logger statements to reduce bundle size
  disableLogger: true,

  // Enables automatic instrumentation of Vercel Cron Monitors. (Does not yet work with App Router route handlers.)
  // See the following for more information:
  // https://docs.sentry.io/product/crons/
  // https://vercel.com/docs/cron-jobs
  automaticVercelMonitors: true,
});
