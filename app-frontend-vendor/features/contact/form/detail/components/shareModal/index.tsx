import styled from "@emotion/styled";
import { Loader, Switch } from "@mantine/core";
import { DriveModal } from "components/Modals/foundation/driveModal";
import PermissionList from "./permisionList";
import { propcolors } from "styles/colors";
import { useCallback, useEffect, useMemo, useState } from "react";
import { ax } from "utils/axios";
import { UserSelect } from "../UserSelect";
import { useRouter } from "next/router";

export type FetchMode = "all" | "partner" | "partner_user";

type PresentationProps = {
  className?: string;
  permissionList: SharedPartner[] | SharedPartnerUser[] | undefined;
  handleFetchMode: (mode: FetchMode) => void;
  onSubmit: (
    value: {
      id: number;
      name: string;
      has_access: boolean;
      partner_name?: string;
    },
    fetchMode: FetchMode
  ) => void;
  selectData:
    | {
        value: {
          id: number;
          has_access: boolean;
        };
        label: string;
        subLabel?: string;
        group: string;
      }[]
    | undefined;
  fetchMode: FetchMode;
  isSharedToAllPartners: boolean | null;
  setIsSharedToAllPartners: (param: boolean) => void;
  description?: string;
};

const Presentation: React.FC<PresentationProps> = ({
  className,
  permissionList,
  handleFetchMode,
  fetchMode,
  onSubmit,
  isSharedToAllPartners,
  setIsSharedToAllPartners,
  selectData,
  description,
}) => {
  if (isSharedToAllPartners === null) {
    return (
      <div className={className}>
        <div className="loading-share">
          <Loader size="lg" />
        </div>
      </div>
    );
  } else {
    return (
      <div className={className}>
        <div className="share-modal-header">
          <h1 className="share-title"> パートナー・ユーザーを選択 </h1>
          {description && (
            <span className="share-notifications">{description}</span>
          )}
          <p className="share-main-label">入力して検索できます</p>
        </div>
        <UserSelect
          data={
            selectData
              ? selectData.map((data) => {
                  return {
                    value: data.value.id,
                    label: data.label,
                    subLabel: data.subLabel,
                    group: data.group,
                    button_label: data.value.has_access ? "削除" : "追加",
                    active: !data.value.has_access,
                  };
                })
              : null
          }
          onClick={(params) => {
            if (params.group === undefined) return;
            onSubmit(
              {
                id: params.value,
                name: params.label,
                has_access: params.active,
              },
              params.group === "パートナー"
                ? "partner"
                : params.group === "全体"
                  ? "all"
                  : "partner_user"
            );
          }}
          isLoading={selectData === undefined}
          isFilter={true}
          isShareAll={isSharedToAllPartners}
        />
        <section className="permission">
          {!isSharedToAllPartners && (
            <>
              <div className="segmented-controller">
                <div
                  className={`segmented-controller-item ${fetchMode === "partner" && "active"}`}
                  onClick={() => {
                    handleFetchMode("partner");
                  }}
                >
                  パートナー
                </div>
                <div className="segmented-controller-separator"></div>
                <div
                  className={`segmented-controller-item ${fetchMode === "partner_user" && "active"}`}
                  onClick={() => {
                    handleFetchMode("partner_user");
                  }}
                >
                  ユーザー
                </div>
              </div>
              <PermissionList
                permissionList={permissionList}
                fetchMode={fetchMode}
                onSubmit={onSubmit}
              />
            </>
          )}
          {isSharedToAllPartners && (
            <>
              <section className="permission-list">
                {isSharedToAllPartners ? (
                  <div className="permission-list-item">
                    <p>全てのパートナー</p>
                    <Switch
                      defaultChecked={isSharedToAllPartners}
                      onChange={(e) => {
                        onSubmit(
                          {
                            id: 0,
                            name: "all",
                            has_access: !isSharedToAllPartners,
                          },
                          "all"
                        );
                      }}
                    />
                  </div>
                ) : (
                  <div className="no-data">no data</div>
                )}
              </section>
            </>
          )}
        </section>
      </div>
    );
  }
};

const Styled = styled(Presentation)`
  padding: 24px;

  .fetching-partner-permission-list {
    display: flex;
    justify-content: center;
    align-items: center;
    width: 100%;
    height: 96px;
  }

  .no-data {
    font-size: 16px;
    font-weight: 600;
    min-height: 48px;
    display: flex;
    justify-content: center;
    align-items: center;
  }

  .user-list-controller {
    padding: 0 24px 24px 24px;
  }

  .partner-list {
    border-radius: 8px;
    overflow: hidden;
    border: 1px solid ${propcolors.gray[200]};
    &-header {
      height: 48px;
      font-size: 14px;
      display: flex;
      color: #222222;
      justify-content: flex-start;
      background: ${propcolors.inputBackground};
      align-items: center;
      border-bottom: 1px solid ${propcolors.gray[200]};
      padding: 10px 16px 10px 16px;
      cursor: default;
    }
    &-item {
      height: 48px;
      padding: 10px 16px 10px 16px;
      display: flex;
      cursor: default;
      font-size: 14px;
      color: #222222;
      transition: 0.2s;
      justify-content: space-between;
      align-items: center;
      background: ${propcolors.white};
      :hover {
        background: ${propcolors.inputBackground};
      }
    }
    &-button {
      height: 32px;
      cursor: pointer;
      border-radius: 6px;
      transition: 0.2s;
      border: 1px solid ${propcolors.blackLight};
      background: ${propcolors.blackLight};
      color: ${propcolors.white};
      padding: 9px 12px 9px 12px;
      &-enable {
        border-color: #c13515;
        background-color: white;
        color: #c13515;
        font-size: 14px;
      }
    }
  }

  height: 100%;
  display: grid;
  grid-template-rows: auto 1fr;
  tr {
    display: grid;
    grid-template-columns: 1fr 10% 15% 15% 15%;
  }

  .segmented-controller {
    width: 100%;
    height: 46px;
    border: 1px solid ${propcolors.gray[200]};
    border-radius: 8px;
    overflow: hidden;
    display: flex;
    justify-content: center;
    align-items: center;
    &-separator {
      width: 1px;
      height: 100%;
      background: ${propcolors.gray[200]};
    }
    &-item {
      transition: 0.3s;
      flex: 50%;
      display: flex;
      justify-content: center;
      align-items: center;
      color: ${propcolors.blackLight};
      height: 100%;
      font-size: 14px;
      cursor: pointer;
      &.active {
        background: ${propcolors.blackLight};
        color: ${propcolors.white};
      }
    }
  }
  .permission {
    margin-top: 24px;
    display: grid;
    grid-template-rows: auto 1fr;
    border-top: 1px solid ${propcolors.gray[200]};
    gap: 24px;
  }
  .permission {
    margin-top: 24px;
    display: grid;
    grid-template-rows: auto 1fr;
    border-top: 1px solid ${propcolors.gray[200]};
    gap: 24px;
    &-list {
      border-radius: 5px;
      &-item {
        max-width: 100%;
        transition: 0.2s;
        border-bottom: 1px solid ${propcolors.gray[200]};
        display: flex;
        justify-content: flex-start;
        align-items: center;
        gap: 16px;
        padding: 16px;
        :hover {
          background-color: ${propcolors.greyBreadcrumb};
        }
        &-switch {
          input:checked + .mantine-Switch-track {
            background-color: ${propcolors.black};
            border-color: ${propcolors.black};
            .mantine-Switch-thumb {
              background-color: ${propcolors.white};
            }
          }
          .mantine-Switch-track {
            height: 20px;
            background-color: ${propcolors.white};
            border-color: #e8eaed;
            .mantine-Switch-thumb {
              background-color: ${propcolors.switchOff};
            }
          }
        }
        &-logo {
          width: 40px;
          height: 40px;
          border-radius: 50%;
          display: flex;
          justify-content: center;
          align-items: center;
          background: ${propcolors.inputBackground};
        }
        &-info {
          flex-grow: 1;
          display: flex;
          justify-content: flex-start;
          items: center;
          flex-direction: column;
          cursor: default;
          gap: 8px;
          &-name {
            font-size: 14px;
            color: #222222;
            font-weight: 400;
            cursor: normal;
            color: ${propcolors.blackLight};
          }
          &-owner {
            font-size: 14px;
            color: #222222;
            font-weight: 400;
            color: ${propcolors.blackLight};
          }
        }
        &-remove {
          display: flex;
          justify-content: center;
          cursor: pointer;
          align-items: center;
        }
        &-switch {
          width: 36px;
          height: 20px;
          input:checked + label {
            background-color: #222222;
            border-color: #222222;
          }
        }
      }
    }
  }
  .share {
    &-notifications {
      color: #f93832;
      margin-bottom: 8px;
      font-weight: 300;
      font-size: 12px;
      display: inline-block;
    }
    &-select {
      padding: 0 24px 24px 24px;
      input {
        height: 48px;
      }
    }
    &-main-label {
      margin-bottom: 8px;
      font-size: 12px;
      color: #666666;
      font-weight: 300;
    }
    &-title {
      font-size: 18px;
      font-bold: 600;
      color: #222222;
      margin-bottom: 16px;
    }
  }
`;

type ShareTrainingModalProps = {
  opened: boolean;
  close: () => void;
  isSharedToAllPartners: boolean;
  setIsSharedToAllPartners: (param: boolean) => void;
  sharingPartnerList: SharedPartner[] | null;
  sharingPartnerUserList: SharedPartnerUser[] | null;
  onSubmit: (
    value: {
      id: number;
      name: string;
      has_access: boolean;
      partner_name?: string;
      partner_id?: number;
    },
    fetchMode: FetchMode
  ) => void;
  description?: string;
  title: string;
};

export const ShareModal: React.FC<ShareTrainingModalProps> = ({
  opened,
  close,
  onSubmit,
  isSharedToAllPartners,
  setIsSharedToAllPartners,
  sharingPartnerList,
  sharingPartnerUserList,
  description,
  title,
}) => {
  const router = useRouter();
  const { uuid } = router.query;
  const [fetchMode, setFetchMode] = useState<FetchMode>("partner");
  const [selectData, setSelectData] = useState<
    {
      value: {
        id: number;
        has_access: boolean;
      };
      label: string;
      group: string;
    }[]
  >();
  const [vendorLinkedPartner, setVendorLinkedPartner] = useState<
    | {
        vendor_linked_partner_id: number;
        partner_name: string;
        partner_id: number;
      }[]
    | null
  >(null);
  const [vendorLinkedPartnerUser, setVendorLinkedPartnerUser] = useState<
    | {
        partner_team_member_id: number;
        name: string;
        email: string;
        has_access: boolean;
        partner_id: number;
      }[]
    | null
  >(null);

  const handleFetchMode = useCallback((mode: FetchMode) => {
    setFetchMode(mode);
  }, []);

  const permissionList = useMemo(() => {
    if (fetchMode === "partner") {
      return sharingPartnerList;
    } else if (fetchMode === "partner_user") {
      return sharingPartnerUserList?.map(
        (item: { id: number; name: string; has_access: boolean }) => {
          const targetPartnerUser = vendorLinkedPartnerUser?.find(
            (allPartner) => allPartner.partner_team_member_id === item.id
          );
          return {
            ...item,
            partner_name: vendorLinkedPartner?.find(
              (partner) => partner.partner_id === targetPartnerUser?.partner_id
            )?.partner_name,
          };
        }
      );
    }
  }, [
    fetchMode,
    sharingPartnerList,
    sharingPartnerUserList,
    vendorLinkedPartnerUser,
    vendorLinkedPartner,
  ]);

  useEffect(() => {
    if (
      vendorLinkedPartner &&
      vendorLinkedPartnerUser &&
      sharingPartnerList &&
      sharingPartnerUserList
    ) {
      const newSelectData: {
        value: {
          id: number;
          has_access: boolean;
        };
        label: string;
        subLabel?: string;
        group: string;
      }[] = [];

      const allPartner = {
        value: {
          id: 0,
          has_access: false,
        },
        label: "全てのパートナー",
        group: "全体",
      };
      newSelectData.push(allPartner);
      vendorLinkedPartner.map((partner) => {
        const sharingPartner = sharingPartnerList.find(
          (item) => item.id === partner.vendor_linked_partner_id
        );
        let partnerHasAccess = false;
        if (sharingPartner) {
          if (sharingPartner.has_access === undefined) {
            // partnerがhas_accessを持っていない場合はpartnerが存在するだけでアクセス権限ありとする
            partnerHasAccess = true;
          } else {
            partnerHasAccess = sharingPartner.has_access;
          }
        }
        newSelectData.push({
          value: {
            id: partner.vendor_linked_partner_id,
            has_access: partnerHasAccess,
          },
          label: partner.partner_name,
          group: "パートナー",
        });
      });
      vendorLinkedPartnerUser.map((partner_user) => {
        const isShared = sharingPartnerUserList.some(
          (item: { id: number; name: string; has_access: boolean }) =>
            item.id === partner_user.partner_team_member_id && item.has_access
        );
        newSelectData.push({
          value: {
            id: partner_user.partner_team_member_id,
            has_access: isShared,
          },
          label: `${partner_user.name} (${partner_user.email})`,
          subLabel: vendorLinkedPartner?.find(
            (partner) => partner.partner_id === partner_user.partner_id
          )?.partner_name,
          group: "ユーザー",
        });
      });
      setSelectData(newSelectData);
    } else if (vendorLinkedPartner === null) {
      ax.get("api/v1/partners/link_active").then((res) => {
        setVendorLinkedPartner(res.data);
      });
      ax.get("api/v1/partners/link_active/users").then((res) => {
        setVendorLinkedPartnerUser(res.data);
      });
    }
  }, [
    vendorLinkedPartner,
    vendorLinkedPartnerUser,
    sharingPartnerList,
    sharingPartnerUserList,
  ]);

  useEffect(() => {
    ax.get(`api/v1/contact/form/${uuid}/permission/all_partners`)
      .then((res) => {
        const result = res.data.data.allPartnersAuthorized;
        setIsSharedToAllPartners(result);
      })
      .catch((err) => {
        // エラーハンドリング
        console.error("Error fetching all partners authorized:", err);
      });
  }, [opened, setIsSharedToAllPartners, uuid]);

  return (
    <DriveModal opened={opened} title={title} close={close}>
      <Styled
        permissionList={permissionList ? permissionList : undefined}
        onSubmit={onSubmit}
        fetchMode={fetchMode}
        handleFetchMode={handleFetchMode}
        isSharedToAllPartners={isSharedToAllPartners}
        setIsSharedToAllPartners={setIsSharedToAllPartners}
        selectData={selectData}
        description={description}
      />
    </DriveModal>
  );
};
