import styled from "@emotion/styled";
import { useRef } from "react";
import { propcolors } from "styles/colors";
import { ThumbnailDropZoneComponent } from "../../thumbnail";
import { LogoDropZoneComponent } from "../../logo";

type PresentationProps = {
  className?: string;
} & ImageSettingProps;

type ImageSettingProps = {
  setContactFormDetail: (contactFormDetail: ContactFormDetail | null) => void;
  contactFormDetail: ContactFormDetail | null;
};

const Presentation: React.FC<PresentationProps> = ({
  className,
  setContactFormDetail,
  contactFormDetail,
}) => {
  const openRef = useRef<() => void>(() => {});
  return (
    <div className={className}>
      <div>
        <p className="label">フォーム内ロゴを設定</p>
        <p className="label">※ 1280px × 720px画像が推奨サイズです</p>
        <LogoDropZoneComponent
          openRef={openRef}
          onDrop={(files) => {
            setContactFormDetail(
              contactFormDetail
                ? {
                  ...contactFormDetail,
                  logo: files[0],
                  logo_path: files.length ? URL.createObjectURL(files[0]) : null,
                  is_logo_updated: true
                }
                : null
            );
          }}
          imagePath={contactFormDetail?.logo_path}
          accept={["image/png", "image/jpeg", "image/jpg"]}
        />
      </div>

      <div>
        <p className="label">フォームサムネイルを設定</p>
        <p className="label">※ 1280px × 720px画像が推奨サイズです</p>
        <ThumbnailDropZoneComponent
          openRef={openRef}
          onDrop={(files) => {
            setContactFormDetail(
              contactFormDetail
                ? {
                  ...contactFormDetail,
                  thumbnail: files[0],
                  thumbnail_path: files.length ? URL.createObjectURL(files[0]) : null,
                  is_thumbnail_updated: true
                }
                : null
            );
          }}
          imagePath={contactFormDetail?.thumbnail_path}
          accept={["image/png", "image/jpeg", "image/jpg"]}
        />
      </div>
    </div>
  );
};

const Styled = styled(Presentation)`
  padding: 16px;
  font-size: 12px;
  display: grid;
  gap: 24px;

  label {
    font-size: 12px;
    font-weight: 600;
  }

  label,
  .label {
    font-weight: 600;
    font-size: 12px;
    color: #666666;
  }

  .label {
    margin-bottom: 8px;
  }

  input {
    font-size: 14px;
  }

  textarea {
    font-size: 14px;
    padding-right: 44px;
  }

  .logo-input {
    border: 1px solid ${propcolors.gray[300]};
    border-radius: 8px;
    background-color: ${propcolors.gray[200]};;
  }
`;
export const ImageSettings: React.FC<ImageSettingProps> = ({
  setContactFormDetail,
  contactFormDetail,
}) => {
  return (
    <Styled
      setContactFormDetail={setContactFormDetail}
      contactFormDetail={contactFormDetail}
    />
  );
};
