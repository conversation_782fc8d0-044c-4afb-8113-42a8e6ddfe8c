import styled from "@emotion/styled";
import React from "react";
import CellUserPartnerNameRender from "../../../leads/components/cell/CellUserPartnerNameRender";
import CellPartnerNameRender from "../../../leads/components/cell/CellPartnerNameRender";

type NameWithIconProps = {
    userName: string;
    thumbnail?: string | null;
}

export const UserNameWithIcon: React.FC<NameWithIconProps> = ({userName, thumbnail}) => {
    return (
        <NameWithIcon>
            <CellUserPartnerNameRender value={ { userName, thumbnail } }/>
        </NameWithIcon>
    )
}

export const CompanyNameWithIcon: React.FC<NameWithIconProps> = ({userName, thumbnail}) => {
    return (
        <NameWithIcon>
            <CellPartnerNameRender value={ { userName, thumbnail } }/>
        </NameWithIcon>
    )
}

const NameWithIcon = styled.div`
  .cell-partner-name-render {
    display: flex;
    align-items: center;

    .cell-partner-name-render-image {
      display: flex;
      align-items: center;
    }
    .cell-partner-name-render-text {
      overflow: hidden;
      text-overflow: ellipsis;
      margin-left: 16px;
    }
  }
  .cell-user-partner-name-render {
    display: flex;
    align-items: center;
    .cell-user-partner-name-render-image {
      display: flex;
      align-items: center;
    }
    .cell-user-partner-name-render-text {
      overflow: hidden;
      text-overflow: ellipsis;
      margin-left: 16px;
    }
  }

`
