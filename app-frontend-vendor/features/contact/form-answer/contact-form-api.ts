import { ax } from "../../../utils/axios";

/**
 * 問い合わせフォームの取得
 * @param formId
 */
export const fetchContactForm = async (formId: string) => {
  const response = await ax.get(`/api/v1/contact/form/${formId}`);
  const contactForm = response.data.contact_form as ContactForm;

  contactForm.form_columns = contactForm.form_columns.map((column) => {
    if (column.column_type === "RADIO" || column.column_type === "CHECKBOX") {
      return {
        ...column,
        select_options: column.select_columns
          ? column.select_columns.map((option) => ({
              key: option.label,
              value: option.uuid,
              sort_order: option.sort_order,
            }))
          : null,
      };
    }
    return column;
  });

  return contactForm;
};

export type ContactForm = {
  uuid: string;
  title: string;
  logo_path: string | null;
  thumbnail_path: string | null;
  description: string;
  form_columns: ContactFormColumn[];
};

export type ContactFormColumn = {
  uuid: string;
  column_type: string;
  label: string;
  description: string | null;
  logo_path: string | null;
  is_required: boolean;
  select_options: SelectOption[] | null;
  select_columns?: ApiSelectOption[] | null;
  sort_order: number;
};

type ApiSelectOption = {
  label: string;
  uuid: string;
  sort_order: number;
};

type SelectOption = {
  key: string;
  value: string;
  sort_order: number;
};

/**
 * 問い合わせ回答（チケット起票）
 * @param formId
 * @param formColumns
 */
export const postForm = async (
  formId: string,
  formColumns: RequestFormColumn[],
  teamMemberId: number | null // 型を number | null に変更
) => {
  await ax.post(`/api/v1/contact/form/${formId}/answer`, {
    form_columns: formColumns,
    ...(teamMemberId !== null && { team_member_id: teamMemberId }), // parseInt は不要に
  });
};

export type RequestFormColumn = {
  uuid: string;
  value: string | null | unknown;
  file_name?: string;
};

/**
 * 問い合わせフォームの共有可能なユーザを取得
 * @param formId
 */
export const fetchFormPermissions = async (formId: string) => {
  const response = await ax.get<FormPermissionsResponse>(
    `/api/v1/contact/form/${formId}/permissions`
  );
  return response.data.permissions;
};

export type LinkedPartner = {
  vendor_linked_partner_id: number;
  partner_name: string;
  partner_id: number;
};

/**
 * 連携パートナーの取得
 * NOTE: 問い合わせフォームの共有ユーザからパートナー情報を取得するため
 */
export const fetchAllLinkedPartners = async (): Promise<
  { id: number; name: string }[]
> => {
  const response = await ax.get<LinkedPartner[]>(
    `/api/v1/partners/link_active`
  );
  return response.data.map((partner) => ({
    id: partner.partner_id,
    name: partner.partner_name,
  }));
};

export type FormPermissionPartner = {
  id: number;
  name: string;
};

export type FormPermissionUser = {
  id: number;
  partner_id: number;
  name: string;
  has_access: boolean;
};

export type FormPermissionsResponse = {
  permissions: {
    shared_linked_partners: FormPermissionPartner[];
    shared_partner_users: FormPermissionUser[];
  };
};
