import styled from "@emotion/styled";
import React, {
  ChangeEvent,
  useCallback,
  useEffect,
  useMemo,
  useRef,
  useState,
} from "react";
import { propcolors } from "../../styles/colors";
import { CommonListLayout } from "../../components/layouts/commonListLayout";
import { CustomBreadcrumb } from "../../components/breadcrumb";
import { Box, Button, Flex, Input, Group, Select } from "@mantine/core";
import { RiSearchLine, RiFilterLine, RiArrowDownSFill } from "@remixicon/react";
import { SSRAGGrid } from "../../components/SSRGrid";
import { AgGridReact } from "ag-grid-react";
import {
  ColDef,
  GridApi,
  GridReadyEvent,
  IGetRowsParams,
} from "ag-grid-community";
import StatusLabel from "./components/StatusLabel";
import { getMailList, getUserMailList } from "./mail-api";
import { formatDateTime } from "../../utils/func/formatDateString";
import { useRouter } from "next/router";
import { setTimeout } from "@sentry-internal/browser-utils";

const filters = [
  { value: "user", label: "作成者が自分のメール" },
  { value: "all", label: "全てのメール" },
];

const Presentation: React.FC = () => {
  const router = useRouter();
  const [selectedFilter, setSelectedFilter] = useState<string>("user");
  const [searchTitle, setSearchTitle] = useState<string>("");
  const gridRef = useRef<AgGridReact>();
  const [gridApi, setGridApi] = useState<GridApi>();
  const onGridReady = (params: GridReadyEvent) => {
    setGridApi(params.api);
  };
  const handleRowClick = (id: string) => {
    router.push(`/mail/${id}`).then();
  };
  const overViewColumns: ColDef[] = [
    {
      field: "management_title",
      headerName: "メール名",
    },
    {
      field: "author",
      headerName: "作成者",
    },
    {
      field: "last_modified_by",
      headerName: "最終更新者",
    },
    {
      field: "schedule",
      headerName: "送信日時",
      cellRenderer: (params: any) => {
        return params.data?.schedule
          ? formatDateTime(params.data.schedule)
          : "";
      },
    },
    {
      field: "status",
      headerName: "ステータス",

      cellRenderer: (params: any) => {
        return params.data?.status !== undefined ? (
          StatusLabel({ status: params.data?.status })
        ) : (
          <></>
        );
      },
      cellStyle: { display: "flex", alignItems: "center" },
    },
    {
      field: "opened",
      headerName: "開封数",
      cellRenderer: (params: any) => {
        return params.data?.opened_count !== undefined &&
          params.data?.send_count !== undefined
          ? `${params.data.opened_count.toLocaleString()}/${params.data.send_count.toLocaleString()}`
          : "";
      },
    },
  ];

  const showNoRow = useCallback(() => {
    if (gridRef.current) {
      gridRef.current.api.showNoRowsOverlay();
    }
  }, []);
  const showLoading = useCallback(() => {
    if (gridRef.current) {
      gridRef.current.api.showLoadingOverlay();
    }
  }, []);
  const hideLoading = useCallback(() => {
    if (gridRef.current) {
      gridRef.current.api.hideOverlay();
    }
  }, []);

  const datasource = useMemo(
    () => ({
      getRows: async (params: IGetRowsParams) => {
        showLoading();
        let page = params.endRow / 20;
        const fetchFunction =
          selectedFilter === "user" ? getUserMailList : getMailList;
        const { mailList, total } = await fetchFunction(page, searchTitle);
        if (mailList.length) {
          params.successCallback(mailList, total);
        } else {
          showNoRow();
          params.successCallback([], 0);
        }
        hideLoading();
      },
    }),
    [selectedFilter, searchTitle, showLoading, showNoRow, hideLoading]
  );

  useEffect(() => {
    gridApi?.setDatasource(datasource);
  }, [gridApi, datasource]);

  useEffect(() => {
    const timer = setTimeout(() => gridApi?.setDatasource(datasource), 500);
    return () => clearTimeout(timer);
  }, [searchTitle, gridApi, datasource]);

  return (
    <CommonListLayout>
      <header>
        <CustomBreadcrumb title={"メール"} list={[{ title: "通常メール" }]} />

        <Flex align={"center"} gap={10}>
          <Button size="xs" onClick={() => router.push("/mail/create")}>
            新規作成
          </Button>
        </Flex>
      </header>
      <Group p={20} css={{ borderTop: `1px solid ${propcolors.gray[200]}` }}>
        <Input
          icon={<RiSearchLine size={18} style={{ color: "red" }} />}
          placeholder={"メール名"}
          value={searchTitle}
          onChange={(e: ChangeEvent<HTMLInputElement>) =>
            setSearchTitle(e.currentTarget.value)
          }
          style={{ width: 300 }}
        />
        <Select
          icon={<RiFilterLine css={{ color: "red", width: "18px" }} />}
          style={{ width: 300 }}
          data={filters}
          value={selectedFilter}
          onChange={(e) => {
            e && setSelectedFilter(e);
          }}
          rightSection={<RiArrowDownSFill />}
          styles={{ rightSection: { pointerEvents: "none" } }}
        />
      </Group>
      <Box>
        <MailTable>
          <SSRAGGrid
            className="grid-height-full"
            columnDefs={overViewColumns}
            onGridReady={onGridReady}
            gridRef={gridRef}
            onRowSelected={(event) => handleRowClick(event.data.id)}
            paginationPageSize={20}
          />
        </MailTable>
      </Box>
    </CommonListLayout>
  );
};

const Styled = styled(Presentation)``;

const MailTable = styled.div`
  .grid-height-full {
    height: 80vh;
    width: 100%;
    .ag-root-wrapper-body {
      min-height: 500px;
    }
  }
`;

export const MailList: React.FC = () => {
  return <Styled />;
};
