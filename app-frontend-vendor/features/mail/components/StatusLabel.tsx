import React from "react";

type StatusLabelProps = {
  status: 0 | 1 | 2;
};

const StatusLabel: React.FC<StatusLabelProps> = ({ status }) => {
  const statuses = {
    0: {
      label: "下書き",
      labelColor: { color: "#1AABF4", backgroundColor: "#D7EEFE" },
    },
    1: {
      label: "送信予約中",
      labelColor: { color: "#13BFB5", backgroundColor: "#CBF4EF" },
    },
    2: {
      label: "送信済",
      labelColor: { color: "#FF569D", backgroundColor: "#FFE5F9" },
    },
  };

  const colors = statuses[status].labelColor;
  const label = statuses[status].label;

  return (
    <span
      style={{
        padding: "6px",
        width: 84,
        height: 26,
        alignItems: "center",
        justifyContent: "center",
        display: "flex",
        fontSize: 12,
        lineHeight: "12px",
        fontWeight: "600",
        borderRadius: 4,
        color: colors?.color,
        backgroundColor: colors?.backgroundColor,
      }}
    >
      {label}
    </span>
  );
};

export default StatusLabel;
