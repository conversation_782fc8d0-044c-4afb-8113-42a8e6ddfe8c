import React, { useEffect, useState } from "react";
import styled from "@emotion/styled";
import { Box, Flex, Group, Radio, Select } from "@mantine/core";
import { DatePickerInput } from "@mantine/dates";
import { RiCalendarView } from "@remixicon/react";
import { propcolors } from "../../../styles/colors";

type SentDateTimeTabPanelProps = {
  initValue: string | null;
  onChange: (date: string | null) => void;
  disabled: boolean;
};

export const SentDateTimeTabPanel: React.FC<SentDateTimeTabPanelProps> = ({
  initValue,
  onChange,
  disabled,
}) => {
  const times = [
    "12:00",
    "12:30",
    "1:00",
    "1:30",
    "2:00",
    "2:30",
    "3:00",
    "3:30",
    "4:00",
    "4:30",
    "5:00",
    "5:30",
    "6:00",
    "6:30",
    "7:00",
    "7:30",
    "8:00",
    "8:30",
    "9:00",
    "9:30",
    "10:00",
    "10:30",
    "11:00",
    "11:30",
  ];

  const [scheduleType, setScheduleType] = useState("now");
  const [date, setDate] = useState<Date>(new Date());
  const [time, setTime] = useState<string | null>("12:00");
  const [ampm, setAmpm] = useState<string | null>("AM");

  useEffect(() => {
    if (!initValue || disabled) return;
    const initDate = new Date(initValue);
    setDate(initDate);

    const hh = initDate.getHours();
    setAmpm(hh >= 12 ? "PM" : "AM");

    const h = hh > 12 ? hh - 12 : hh === 0 ? 12 : hh;
    const m = initDate.getMinutes().toString().padStart(2, "0");
    setTime(`${h}:${m}`);
    setScheduleType("schedule");
  }, [initValue, disabled]);

  useEffect(() => {
    if (scheduleType === "now" || !time || !ampm) {
      onChange(null);
    } else {
      const newDate = new Date(`${date?.toLocaleDateString()} ${time} ${ampm}`);
      const year = newDate.getFullYear();
      const month = String(newDate.getMonth() + 1).padStart(2, "0");
      const day = String(newDate.getDate()).padStart(2, "0");
      const hours = String(newDate.getHours()).padStart(2, "0");
      const minutes = String(newDate.getMinutes()).padStart(2, "0");
      onChange(`${year}-${month}-${day} ${hours}:${minutes}:00`);
    }
  }, [scheduleType, date, time, ampm]);

  return (
    <Styled>
      <Box px={20} pt={30}>
        <Radio.Group
          name="favoriteFramework"
          value={scheduleType}
          onChange={async (value) => {
            setScheduleType(value);
          }}
        >
          <Group mt="xs">
            <Radio value="now" label="今すぐ送信する" disabled={disabled} />
            <Radio
              value="schedule"
              label="送信日時を指定する"
              disabled={disabled}
            />
          </Group>
        </Radio.Group>

        {scheduleType === "schedule" && (
          <Flex mt={20} align="center" gap={15}>
            <DatePickerInput
              w={200}
              label="送信日"
              dropdownType="modal"
              value={date}
              defaultValue={date}
              valueFormat="YYYY年M月D日"
              locale="ja"
              monthLabelFormat="YYYY年M月"
              yearLabelFormat="YYYY年"
              monthsListFormat="M"
              yearsListFormat="YYYY"
              firstDayOfWeek={0}
              onChange={(e) => e && setDate(e)}
              icon={<RiCalendarView color={propcolors.partnerRed} size={20} />}
            />
            <Select
              data={times}
              label="時間"
              w={120}
              defaultValue={times[0]}
              value={time}
              onChange={(value) => {
                setTime(value);
              }}
            />
            <Select
              data={["AM", "PM"]}
              label=" "
              w={80}
              defaultValue={"AM"}
              value={ampm}
              onChange={(value) => {
                setAmpm(value);
              }}
            />
          </Flex>
        )}
      </Box>
    </Styled>
  );
};

const Styled = styled.div``;
