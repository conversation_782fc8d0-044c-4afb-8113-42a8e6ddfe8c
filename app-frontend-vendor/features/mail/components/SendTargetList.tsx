import { Box, Checkbox, Group, Radio, Text } from "@mantine/core";
import { usePartnerGroupList } from "../../settings/partner-group/hooks/usePartnerGroupList";
import React, { ReactNode, useEffect, useState } from "react";
import { ax } from "../../../utils/axios";

type SendTargetListProps = {
  targetPartners: number[];
  targetGroups: number[];
  setTargetPartners: (newTarget: number[]) => void;
  setTargetGroups: (newTarget: number[]) => void;
  error: ReactNode;
  disabled: boolean;
};

export const SendTargetList: React.FC<SendTargetListProps> = ({
  targetPartners,
  targetGroups,
  setTargetPartners,
  setTargetGroups,
  error,
  disabled,
}) => {
  const [partners, setPartners] = useState([]);
  const { partnerGroups } = usePartnerGroupList();
  const [radioSendTarget, setRadioSendTarget] = useState("partner");
  const [groupInput, setGroupInput] = useState<
    { id: number; name: string; checked: boolean }[]
  >([]);
  const [partnersInput, setPartnersInput] = useState<
    { id: number; name: string; checked: boolean }[]
  >([]);

  useEffect(() => {
    if (targetPartners.length) {
      setRadioSendTarget("partner");
    }
    if (targetGroups.length) {
      setRadioSendTarget("group");
    }
  }, [targetPartners, targetGroups]);

  useEffect(() => {
    (async () => {
      const response = await ax.get("api/v1/partners/link_active");
      const partners = response.data;
      setPartners(partners);
    })();
  }, []);

  useEffect(() => {
    (async () => {
      if (!partners.length) return;

      const input = partners.map((partner: any) => {
        return {
          id: partner.vendor_linked_partner_id,
          name: partner.partner_name,
          checked:
            targetPartners.find(
              (target) => target === partner.vendor_linked_partner_id
            ) !== undefined,
        };
      });
      setPartnersInput(input);
    })();
  }, [partners, targetPartners]);

  useEffect(() => {
    if (!partnerGroups.length) return;
    const input = partnerGroups.map((group) => {
      return {
        id: group.id,
        name: group.name,
        checked:
          targetGroups.find((target) => target === group.id) !== undefined,
      };
    });
    setGroupInput(input);
  }, [partnerGroups, targetGroups]);

  const handleChangeRadio = (radioValue: string) => {
    if (radioValue === "partner") {
      const newInput = partnersInput
        .filter((partner) => partner.checked)
        .map((partner) => partner.id);
      setTargetPartners(newInput);
      setTargetGroups([]);
    } else {
      const newInput = groupInput
        .filter((group) => group.checked)
        .map((group) => group.id);
      setTargetGroups(newInput);
      setTargetPartners([]);
    }
    setRadioSendTarget(radioValue);
  };

  const handleAllPartnerCheck = (checked: boolean) => {
    const input = partnersInput.map((partner) => {
      return {
        ...partner,
        checked,
      };
    });
    setPartnersInput(input);

    const newTarget = input
      .filter((partner) => partner.checked)
      .map((partner) => partner.id);
    setTargetPartners(newTarget);
  };

  const handleAllGroupCheck = (checked: boolean) => {
    const input = groupInput.map((group) => {
      return {
        ...group,
        checked,
      };
    });
    setGroupInput(input);
    const newTarget = input
      .filter((group) => group.checked)
      .map((group) => group.id);
    setTargetGroups(newTarget);
  };

  const handlePartnerCheck = (index: number, checked: boolean) => {
    const newInput = [...partnersInput];
    newInput[index].checked = checked;
    setPartnersInput(newInput);
    const newTarget = partnersInput
      .filter((partner) => partner.checked)
      .map((partner) => partner.id);
    setTargetPartners(newTarget);
  };

  const handleGroupCheck = (index: number, checked: boolean) => {
    const newInput = [...groupInput];
    newInput[index].checked = checked;
    setGroupInput(newInput);
    const newTarget = groupInput
      .filter((group) => group.checked)
      .map((group) => group.id);
    setTargetGroups(newTarget);
  };

  return (
    <Box px={20} pt={30}>
      {error && (
        <Text color={"red"} size={"xs"}>
          {error}
        </Text>
      )}
      <Radio.Group value={radioSendTarget} onChange={handleChangeRadio}>
        <Group mt="xs">
          <Radio
            value="partner"
            label="パートナー企業を指定して送信"
            disabled={disabled}
          />
          <Radio
            value="group"
            label="パートナーグループを指定して送信"
            disabled={disabled}
          />
        </Group>
      </Radio.Group>

      {radioSendTarget === "partner" && (
        <Box>
          <Checkbox
            label="すべてのパートナー企業を選択する"
            mt={25}
            ml={10}
            size="xs"
            onChange={(event) =>
              handleAllPartnerCheck(event.currentTarget.checked)
            }
            disabled={disabled}
          />
          <Box h={800} w={"100%"} className="target-list">
            {partnersInput.map((partner: any, index) => (
              <Checkbox
                label={partner.name}
                mt={25}
                ml={10}
                size="xs"
                checked={partner.checked}
                onChange={(event) =>
                  handlePartnerCheck(index, event.currentTarget.checked)
                }
                key={index}
                disabled={disabled}
              />
            ))}
          </Box>
        </Box>
      )}

      {radioSendTarget === "group" && (
        <Box>
          <Checkbox
            label="すべてのグループを選択する"
            onChange={(event) =>
              handleAllGroupCheck(event.currentTarget.checked)
            }
            mt={25}
            ml={10}
            size="xs"
            disabled={disabled}
          />
          <Box h={800} w={"100%"} className="target-list">
            {groupInput.map((group, index) => (
              <Checkbox
                label={group.name}
                mt={25}
                ml={10}
                size="xs"
                checked={group.checked}
                onChange={(event) =>
                  handleGroupCheck(index, event.currentTarget.checked)
                }
                key={index}
                disabled={disabled}
              />
            ))}
          </Box>
        </Box>
      )}
    </Box>
  );
};
