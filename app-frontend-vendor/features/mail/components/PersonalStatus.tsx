import React from "react";

type PersonalStatusProps = {
  opened_at: string | null;
  status: number | undefined;
};

export const PersonalStatus: React.FC<PersonalStatusProps> = ({
  opened_at,
  status,
}) => {
  const statuses = {
    opened: {
      label: "開封済",
      labelColor: { color: "#FF569D", backgroundColor: "#FFE5F9" },
    },
    unopened: {
      label: "未開封",
      labelColor: { color: "#1AABF4", backgroundColor: "#D7EEFE" },
    },
    failed: {
      label: "送信失敗",
      labelColor: { color: "#8992A0", backgroundColor: "#E8EAED" },
    },
  };

  let colors = null;
  let label = null;

  if (status === 2) {
    colors = statuses.failed.labelColor;
    label = statuses.failed.label;
  } else if (opened_at) {
    colors = statuses.opened.labelColor;
    label = statuses.opened.label;
  } else {
    colors = statuses.unopened.labelColor;
    label = statuses.unopened.label;
  }

  return (
    <span
      style={{
        padding: "6px",
        width: 84,
        height: 26,
        alignItems: "center",
        justifyContent: "center",
        display: "flex",
        fontSize: 12,
        lineHeight: "12px",
        fontWeight: "600",
        borderRadius: 4,
        color: colors?.color,
        backgroundColor: colors?.backgroundColor,
      }}
    >
      {label}
    </span>
  );
};
