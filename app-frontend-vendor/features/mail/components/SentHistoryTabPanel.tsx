import React, { useEffect } from "react";
import styled from "@emotion/styled";
import { Box, Flex, Text } from "@mantine/core";
import { propcolors } from "../../../styles/colors";
import { Mail } from "../mail-api";
import { useMailHistory } from "../hooks/useMailHistory";
import IconUserPartner from "public/icons/user-icon.svg";
import { formatDateTime } from "../../../utils/func/formatDateString";
import { PersonalStatus } from "./PersonalStatus";
import { MailStatus } from "../mail-const";
type SentHistoryTabPanelProps = {
  mail: Mail | undefined;
};

export const SentHistoryTabPanel: React.FC<SentHistoryTabPanelProps> = ({
  mail,
}) => {
  const { mailHistory, setMailId } = useMailHistory();

  useEffect(() => {
    if (!mail) return;
    (async () => {
      setMailId(mail.id);
    })();
  }, [mail]);

  if (!mail || mail.status !== MailStatus.MAIL_COMPLETED) {
    return (
      <Box px={20} pt={30}>
        開封履歴はありません。
      </Box>
    );
  }

  return (
    <Styled>
      <Box
        px={20}
        pt={30}
        style={{ overflowY: "scroll", maxHeight: "calc(100vh - 0px)" }}
      >
        <Box>
          <Text fw={"bold"}>
            送信日時: {mail.schedule ? formatDateTime(mail.schedule) : ""}
          </Text>
          <Text color={propcolors.red[800]} fz={"xs"} fw={"bold"}>
            ※メールを一斉送信するため、送信日時は目安の時間になります。
          </Text>
        </Box>

        {mailHistory && (
          <>
            <Flex mt={20} gap={15}>
              <Flex
                w={120}
                h={78}
                bg={propcolors.black}
                align={"center"}
                direction={"column"}
                justify={"center"}
                style={{ borderRadius: "10px" }}
              >
                <Text fz={"xs"} color={propcolors.white}>
                  送信数
                </Text>
                <Text color={propcolors.white}>{mailHistory?.success}件</Text>
              </Flex>

              <Flex
                w={120}
                h={78}
                bg={"#D7EEFE"}
                align={"center"}
                direction={"column"}
                justify={"center"}
                style={{ borderRadius: "10px" }}
              >
                <Text fz={"xs"} color={"#1AABF4"}>
                  未開封数
                </Text>
                <Text color={"#1AABF4"}>{mailHistory?.unopened}件</Text>
              </Flex>

              <Flex
                w={120}
                h={78}
                bg={"#FFE5F9"}
                align={"center"}
                direction={"column"}
                justify={"center"}
                style={{ borderRadius: "10px" }}
              >
                <Text fz={"xs"} color={"#FF569D"}>
                  開封数
                </Text>
                <Text color={"#FF569D"}>{mailHistory?.opened}件</Text>
              </Flex>

              <Flex
                w={120}
                h={78}
                bg={"#E8EAED"}
                align={"center"}
                direction={"column"}
                justify={"center"}
                style={{ borderRadius: "10px" }}
              >
                <Text fz={"xs"} color={"#8992A0"}>
                  送信エラー数
                </Text>
                <Text color={"#8992A0"}>{mailHistory?.failed}件</Text>
              </Flex>
            </Flex>

            <Box mt={20} mb={20}>
              {mailHistory?.histories.map((history) => (
                <Box mt={20} key={history.partner_id}>
                  <Text fw={"bold"}>{history.partner_name}</Text>
                  {history?.partner_users?.map((user, index) => (
                    <Box mt={10} key={index}>
                      <Flex justify={"space-between"}>
                        <Flex align={"center"}>
                          <IconUserPartner />
                          <Box w={300} ml={10}>
                            <Text>{user.name}</Text>
                            <Text fz={"xs"} color={propcolors.gray[500]}>
                              {user.email}
                            </Text>
                          </Box>
                          <PersonalStatus
                            opened_at={user.opened_at}
                            status={user.status}
                          />
                        </Flex>
                        <Flex align={"center"}>
                          <Box>
                            {user.opened_at
                              ? formatDateTime(user.opened_at)
                              : ""}
                          </Box>
                        </Flex>
                      </Flex>
                    </Box>
                  ))}
                </Box>
              ))}
            </Box>
          </>
        )}
      </Box>
    </Styled>
  );
};

const Styled = styled.div``;
