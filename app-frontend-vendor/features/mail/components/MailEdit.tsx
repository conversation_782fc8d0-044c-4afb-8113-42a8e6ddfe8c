"use client";
import styled from "@emotion/styled";
import {
  ActionIcon,
  Box,
  Button,
  Menu,
  Tabs,
  Text,
  TextInput,
} from "@mantine/core";
import React, { useEffect, useRef, useState } from "react";
import { CustomBreadcrumb } from "../../../components/breadcrumb";
import { propcolors } from "../../../styles/colors";
import { RiCodeSSlashLine, RiDoubleQuotesL } from "@remixicon/react";
import { useRouter } from "next/router";
import { SentDateTimeTabPanel } from "./SentDateTimeTabPanel";
import { SentHistoryTabPanel } from "./SentHistoryTabPanel";
import { SendTargetList } from "./SendTargetList";
import { useForm } from "@mantine/form";
import { Mail, MailRequest, sendTestMail } from "../mail-api";
import { notifications } from "@mantine/notifications";
import IconNotiSuccess from "../../../public/icons/icon-noti-success.svg";
import { RichTextEditor } from "@mantine/tiptap";
import { StarterKit } from "@tiptap/starter-kit";
import { useEditor } from "@tiptap/react";
import { TextStyle } from "@tiptap/extension-text-style";
import Link from "@tiptap/extension-link";
import Image from "@tiptap/extension-image";
import HardBreak from "@tiptap/extension-hard-break";
import { convertFileToBase64 } from "../../../utils/func/base64Converter";
import { MailStatus } from "../mail-const";

type MaileEditProps = {
  mail?: Mail | undefined;
  loading?: boolean;
  handleSend: (mailRequest: MailRequest) => Promise<void>;
  handleDraft: (mailRequest: MailRequest) => Promise<void>;
};

export const MailEdit: React.FC<MaileEditProps> = ({
  mail,
  loading = false,
  handleSend,
  handleDraft,
}) => {
  const router = useRouter();
  const [currentTab, setCurrentTab] = useState<string | null>("datetime");
  const [testAddress, setTestAddress] = useState("");
  const [testAddressError, setTestAddressError] = useState(false);
  const [isMailSending, setIsMailSending] = useState(false);
  const [isTestMailSending, setIsTestMailSending] = useState(false);

  const form = useForm<MailRequest>({
    initialValues: {
      management_title: "",
      subject: "",
      content: "",
      datetime: null,
      send_to: [],
      send_groups: [],
    },
    validate: {
      management_title: (value) => (value ? null : "必須項目です。"),
      subject: (value) => (value ? null : "必須項目です。"),
      content: (value) => {
        if (value === "<p></p>") return "必須項目です。";
        if (value === "") return "必須項目です。";
        return null;
      },
      send_to: (value, values) =>
        value.length || values.send_groups.length
          ? null
          : "送信対象を選択してください。",
      send_groups: (value, values) =>
        value.length || values.send_to.length
          ? null
          : "送信対象を選択してください。",
    },
  });

  useEffect(() => {
    form.setFieldValue("management_title", mail?.management_title ?? "");
    form.setFieldValue("subject", mail?.subject ?? "");
    form.setFieldValue("content", mail?.content ?? "");
    form.setFieldValue("datetime", mail?.schedule ?? null);
    form.setFieldValue("send_to", mail?.send_partners ?? []);
    form.setFieldValue("send_groups", mail?.send_groups ?? []);
  }, [mail]);

  useEffect(() => {
    if (mail?.status === MailStatus.MAIL_COMPLETED) {
      setCurrentTab("history");
    }
  }, [mail]);

  const onClickSend = async () => {
    if (!validate()) return;
    setIsMailSending(true);
    try {
      await handleSend({ ...form.values, content: replaceUiTagToBackTag() });
      notifications.show({
        message: form.values.datetime ? "更新しました。" : "送信しました。",
        icon: <IconNotiSuccess />,
      });
    } catch (error) {
      
    } finally {
      setIsMailSending(false);
    }
  };

  const onClickDraft = async () => {
    if (!validate()) return;
    await handleDraft({ ...form.values, content: replaceUiTagToBackTag() });
    notifications.show({
      message: "更新しました。",
      icon: <IconNotiSuccess />,
    });
  };

  const validate = () => {
    const valid = form.validate();
    if (valid.hasErrors) {
      if (valid.errors.send_to) setCurrentTab("sendTo");
      return false;
    }
    return true;
  };

  const setDatetime = (date: string | null) => {
    form.setFieldValue("datetime", date);
  };

  const setSendTo = (newSendTo: number[]) => {
    form.setFieldValue("send_to", newSendTo);
  };

  const setSendGroups = (newSendGroups: number[]) => {
    form.setFieldValue("send_groups", newSendGroups);
  };

  const sendTest = async () => {
    if (!validateTestMail()) return;
    setIsTestMailSending(true);
    try {
      await sendTestMail({
        to: testAddress,
        subject: form.values.subject,
        content: replaceUiTagToBackTag(),
      });
      notifications.show({
        message: "送信しました。",
        icon: <IconNotiSuccess />,
      });
    } catch(error) {

    } finally {
      setIsTestMailSending(false);
    }
  };

  const validateTestMail = () => {
    setTestAddressError(false);
    let result = true;
    if (form.values.subject === "") {
      form.setFieldError("subject", "必須項目です");
      result = false;
    }
    if (form.values.content === "" || form.values.content === "<p></p>") {
      form.setFieldError("content", "必須項目です");
      result = false;
    }
    if (!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(testAddress)) {
      setTestAddressError(true);
      result = false;
    }
    return result;
  };

  const editor = useEditor({
    extensions: [
      StarterKit,
      TextStyle,
      Link.configure({
        openOnClick: false,
      }),
      Image.configure({
        allowBase64: true,
        inline: true,
      }),
      HardBreak.configure({ keepMarks: false }).extend({
        addKeyboardShortcuts() {
          return {
            Enter: () => this.editor.commands.setHardBreak(),
          };
        },
      }),
    ],
    immediatelyRender: false,
    onUpdate: ({ editor }) => {
      form.setFieldValue("content", editor.getHTML());
    },
    editorProps: {
      handlePaste(view, event, slice) {
        if (event.clipboardData) {
          const text = event.clipboardData.getData("text/plain");
          const html = text.replace(/(\r\n|\n|\r)/g, "<br>");
          view.pasteHTML(html);
          return true;
        }
        return false;
      },
    },
  });

  const replaceUiTagToBackTag = () => {
    let html = editor?.getHTML() ?? "";
    html = html.replaceAll(
      '<img src="/icons/partner_user_name.png">',
      "{{partner_user_name}}"
    );
    html = html.replaceAll(
      '<img src="/icons/partner_name.png">',
      "{{partner_name}}"
    );
    html = html.replaceAll(
      '<img src="/icons/partner_login_url.png">',
      "{{partner_login_url}}"
    );
    return html;
  };

  const replaceBackTagToUiTag = (content: string) => {
    content = content.replaceAll(
      "{{partner_user_name}}",
      '<img src="/icons/partner_user_name.png">'
    );
    content = content.replaceAll(
      "{{partner_name}}",
      '<img src="/icons/partner_name.png">'
    );
    content = content.replaceAll(
      "{{partner_login_url}}",
      '<img src="/icons/partner_login_url.png">'
    );
    return content;
  };

  useEffect(() => {
    form.setFieldValue("content", mail?.content ?? "");
    editor
      ?.chain()
      .setContent(replaceBackTagToUiTag(mail?.content ?? ""))
      .run();
  }, [editor, mail]);

  const insertCustomTag = (tag: string) => {
    switch (tag) {
      case "partnerName":
        editor
          ?.chain()
          .focus()
          .insertContent('&nbsp;<img src="/icons/partner_name.png">&nbsp;')
          .run();
        break;
      case "partnerUserName":
        editor
          ?.chain()
          .focus()
          .insertContent('&nbsp;<img src="/icons/partner_user_name.png">&nbsp;')
          .run();
        break;
      case "partnerURL":
        editor
          ?.chain()
          .focus()
          .insertContent('&nbsp;<img src="/icons/partner_login_url.png">&nbsp;')
          .run();
        break;
      default:
        throw new Error();
    }
  };

  const insertImage = async (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files && event.target.files[0];
    if (!file) return;
    const base64Image = await convertFileToBase64(file);
    editor
      ?.chain()
      .focus()
      .setImage({ src: base64Image as string })
      .run();
  };
  const ref = useRef<HTMLInputElement>(null);

  return (
    <Styled>
      <header>
        <CustomBreadcrumb title="メール" list={[{ title: "通常メール" }]} />
        <Box className="header-button-container">
          <Button variant="default" onClick={() => router.push("/mail")}>
            キャンセル
          </Button>
          <Button
            variant="default"
            onClick={onClickDraft}
            disabled={mail?.status === MailStatus.MAIL_COMPLETED}
          >
            下書きで保存
          </Button>
          <Button
            onClick={onClickSend}
            disabled={mail?.status === MailStatus.MAIL_COMPLETED}
            loading={isMailSending}
          >
            送信
          </Button>
        </Box>
      </header>

      <Box className="main">
        <Box className="mail-inputs">
          <Text>メール配信設定</Text>
          <Box mt={30}>
            <TextInput
              label="メールタイトル(管理用)"
              {...form.getInputProps("management_title")}
              required
              disabled={mail?.status === MailStatus.MAIL_COMPLETED}
            />
          </Box>
          <Box mt={30}>
            <TextInput
              label="メール件名"
              {...form.getInputProps("subject")}
              required
              disabled={mail?.status === MailStatus.MAIL_COMPLETED}
            />
          </Box>

          <Box mt={50}>
            {form.errors.content && (
              <Text color={"red"} size={"xs"}>
                必須項目です
              </Text>
            )}
            <Box className="mail-editor">
              <RichTextEditor editor={editor} className={"toolbar"}>
                <RichTextEditor.Toolbar sticky stickyOffset={60}>
                  <RichTextEditor.Bold />
                  <RichTextEditor.Italic />
                  <RichTextEditor.H1 />
                  <RichTextEditor.H2 />
                  <RichTextEditor.Link />
                  {/* todo 画像の仕様が決まり次第復活　*/}
                  {/*<RichTextEditor.Control onClick={() => ref.current?.click()}>*/}
                  {/*  <RiImageLine size={16} />*/}
                  {/*</RichTextEditor.Control>*/}
                  {/*<input*/}
                  {/*  type={"file"}*/}
                  {/*  ref={ref}*/}
                  {/*  hidden*/}
                  {/*  onChange={insertImage}*/}
                  {/*  accept="image/gif, image/jpg, image/jpeg, image/png"*/}
                  {/*/>*/}
                  <RichTextEditor.Control
                    onClick={() =>
                      editor?.chain().focus().setBlockquote().run()
                    }
                  >
                    <RiDoubleQuotesL size={16} />
                  </RichTextEditor.Control>
                  <Menu>
                    <Menu.Target>
                      <ActionIcon color={"dark"}>
                        <RiCodeSSlashLine size={16} />
                      </ActionIcon>
                    </Menu.Target>
                    <Menu.Dropdown>
                      <Menu.Item
                        onClick={() => insertCustomTag("partnerUserName")}
                      >
                        パートナーユーザー名
                      </Menu.Item>
                      <Menu.Item onClick={() => insertCustomTag("partnerName")}>
                        パートナー企業名
                      </Menu.Item>
                      <Menu.Item onClick={() => insertCustomTag("partnerURL")}>
                        パートナーURL
                      </Menu.Item>
                    </Menu.Dropdown>
                  </Menu>
                </RichTextEditor.Toolbar>
              </RichTextEditor>

              <Box mt={20} p={20} pos={"relative"}>
                <Box h={50} className={"mail-header"} bg={"#000000"} />
                <RichTextEditor editor={editor}>
                  <RichTextEditor.Content />
                </RichTextEditor>
              </Box>
            </Box>
          </Box>
        </Box>

        <Box className="mail-settings" w={"50%"}>
          <Tabs
            w={"100%"}
            defaultValue="datetime"
            value={currentTab}
            onTabChange={setCurrentTab}
          >
            <Tabs.List grow h={50}>
              <Tabs.Tab value="datetime">送信日時</Tabs.Tab>
              <Tabs.Tab value="sendTo">送付先</Tabs.Tab>
              <Tabs.Tab value="test">テスト</Tabs.Tab>
              <Tabs.Tab value="history">開封履歴</Tabs.Tab>
            </Tabs.List>

            <Tabs.Panel value="datetime">
              {!loading && (
                <SentDateTimeTabPanel
                  onChange={setDatetime}
                  initValue={mail?.schedule ?? null}
                  disabled={mail?.status === MailStatus.MAIL_COMPLETED}
                />
              )}
            </Tabs.Panel>
            <Tabs.Panel value="sendTo">
              {!loading && (
                <SendTargetList
                  targetPartners={form.values.send_to}
                  targetGroups={form.values.send_groups}
                  setTargetPartners={setSendTo}
                  setTargetGroups={setSendGroups}
                  error={form.errors.sent_to || form.errors.send_groups}
                  disabled={mail?.status === MailStatus.MAIL_COMPLETED}
                />
              )}
            </Tabs.Panel>
            <Tabs.Panel value="test">
              <Box px={20} pt={30}>
                <TextInput
                  label="テスト用-送信先のメールアドレス"
                  placeholder="<EMAIL>"
                  type={"email"}
                  value={testAddress}
                  onChange={(event) =>
                    setTestAddress(event.currentTarget.value)
                  }
                  disabled={mail?.status === MailStatus.MAIL_COMPLETED}
                />
                {testAddressError && (
                  <Text color={"red"} size={"xs"}>
                    メールアドレスを入力してください
                  </Text>
                )}
                <Button
                  color="dark"
                  size="xs"
                  mt={15}
                  onClick={sendTest}
                  disabled={mail?.status === MailStatus.MAIL_COMPLETED}
                  loading={isTestMailSending}
                >
                  テストメール送信
                </Button>
              </Box>
            </Tabs.Panel>
            <Tabs.Panel value="history">
              <SentHistoryTabPanel mail={mail} />
            </Tabs.Panel>
          </Tabs>
        </Box>
      </Box>
    </Styled>
  );
};

const Styled = styled.div`
  header {
    display: flex;
    justify-content: space-between;
    padding: 10px 20px;
    border-top: solid 1px ${propcolors.gray[200]};
    border-bottom: solid 1px ${propcolors.gray[200]};
  }

  .header-button-container {
    display: flex;
    gap: 15px;
  }

  .main {
    display: flex;
  }

  .mail-inputs {
    width: 50%;
    padding: 30px;
  }

  .ql-toolbar {
    background-color: ${propcolors.white};
    border-bottom: solid 1px ${propcolors.gray[300]};
    display: flex;
    align-items: center;
  }

  .mail-editor {
    background-color: ${propcolors.gray[200]};
    border: solid 1px ${propcolors.gray[300]};
    height: 100%;
  }

  .mail-header {
    border-radius: 10px 10px 0 0;
  }

  .mantine-RichTextEditor-content {
    min-height: 500px;
    background-color: ${propcolors.white};
    font-size: 1rem;
    padding-top: 50px;
  }

  .mail-settings {
    width: 50%;
    min-height: 100%;
    border-left: solid 1px ${propcolors.gray[200]};
  }

  .target-list {
    border: solid 1px ${propcolors.gray[200]};
    border-radius: 5px;
    overflow-y: scroll;
    margin-top: 5px;
  }

  .mantine-DatePickerInput-input {
    padding-left: 10px;
  }

  .mantine-DatePickerInput-icon {
    left: unset;
    right: 0;
  }

  .toolbar {
    z-index: 3;
  }

  .tiptap span {
    color: red;
    border: red solid 1px;
    border-radius: 3px;
    padding: 1px 15px;
    margin-bottom: 5px;
    display: inline-block;
  }

  .tiptap a {
    color: blue;
  }
  .tiptap img[src="/icons/partner_name.png"] {
    margin-top: 8px;
    vertical-align: middle;
    width: 134px;
  }

  .tiptap img[src="/icons/partner_user_name.png"] {
    margin-top: 8px;
    vertical-align: middle;
    width: 158px;
  }
  .tiptap img[src="/icons/partner_login_url.png"] {
    margin-top: 8px;
    vertical-align: middle;
    width: 170px;
  }
  .mantine-RichTextEditor-control {
    border: 0;
  }
`;
