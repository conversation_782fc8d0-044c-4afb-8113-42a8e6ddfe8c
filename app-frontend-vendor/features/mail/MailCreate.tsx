import { MailEdit } from "./components/MailEdit";
import {
  createDraftMail,
  createScheduleMail,
  MailRequest,
  sendMail,
} from "./mail-api";
import { useRouter } from "next/router";

export const MailCreate = () => {
  const router = useRouter();

  const handleSend = async (mailRequest: MailRequest) => {
    if (mailRequest.datetime) {
      await createScheduleMail(mailRequest);
    } else {
      await sendMail(mailRequest);
    }
    router.push("/mail");
  };

  const handleDraft = async (mailRequest: MailRequest) => {
    await createDraftMail(mailRequest);
    router.push("/mail");
  };

  return <MailEdit handleSend={handleSend} handleDraft={handleDraft} />;
};
