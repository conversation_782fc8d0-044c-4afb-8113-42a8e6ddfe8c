import { useEffect, useState } from "react";
import { getHistory, SendHistory } from "../mail-api";

export const useMailHistory = () => {
  const [mailHistory, setMailHistory] = useState<SendHistory>();
  const [mailId, setMailId] = useState<number>();

  useEffect(() => {
    if (!mailId) return;
    (async () => {
      const history = await getHistory(mailId);
      setMailHistory(history);
    })();
  }, [mailId]);

  return { mailHistory, setMailId };
};
