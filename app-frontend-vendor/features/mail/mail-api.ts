import { ax } from "../../utils/axios";
import { updateErrorHandler } from "./components/error-handle";

export const getMailList = async (
  page: number = 1,
  title: string
): Promise<{
  mailList: MailListItem[];
  total: number;
}> => {
  const response = await ax.get(`/api/v1/mail/all?page=${page}&title=${title}`);

  return { mailList: response.data.data, total: response.data.total };
};

export const getUserMailList = async (
  page: number = 1,
  title: string
): Promise<{
  mailList: MailListItem[];
  total: number;
}> => {
  const response = await ax.get(
    `/api/v1/mail/user?page=${page}&title=${title}`
  );

  return { mailList: response.data.data, total: response.data.total };
};

export const createScheduleMail = async (payload: MailRequest) => {
  await ax
    .post(`/api/v1/mail/schedule`, payload)
    .catch((e) => updateErrorHandler(e));
};

export const sendMail = async (payload: MailRequest) => {
  await ax
    .post(`/api/v1/mail/send`, payload)
    .catch((e) => updateErrorHandler(e));
};

export const createDraftMail = async (payload: MailRequest) => {
  await ax
    .post(`/api/v1/mail/draft`, payload)
    .catch((e) => updateErrorHandler(e));
};

export const sendDraftMail = async (mailId: number, payload: MailRequest) => {
  await ax
    .post(`/api/v1/mail/send/${mailId}`, payload)
    .catch((e) => updateErrorHandler(e));
};

export const updateScheduleMail = async (
  mailId: number,
  payload: MailRequest
) => {
  await ax
    .put(`/api/v1/mail/schedule/${mailId}`, payload)
    .catch((e) => updateErrorHandler(e));
};

export const updateDraftMail = async (mailId: number, payload: MailRequest) => {
  await ax
    .put(`/api/v1/mail/draft/${mailId}`, payload)
    .catch((e) => updateErrorHandler(e));
};

export const sendTestMail = async (testMail: TestMail) => {
  await ax
    .post(`/api/v1/mail/test`, testMail)
    .catch((e) => updateErrorHandler(e));
};

export const getHistory = async (mailId: number): Promise<SendHistory> => {
  const response = await ax.get(`/api/v1/mail/${mailId}/history`);
  return response.data;
};

export type Mail = {
  id: number;
  management_title: string;
  content: string;
  subject: string;
  schedule: string | null;
  status: MailStatus;
  send_to: number[];
  send_partners: number[];
  send_groups: number[];
  created_at: string;
  updated_at: string;
};

export type MailListItem = Mail & {
  opened_count: number;
  send_count: number;
};

export type MailRequest = {
  management_title: string;
  subject: string;
  content: string;
  datetime: string | null;
  send_to: number[];
  send_groups: number[];
};

export type TestMail = {
  to: string;
  subject: string;
  content: string;
};

export type SendHistory = {
  success: number;
  failed: number;
  opened: number;
  unopened: number;
  histories: History[];
};

export type History = {
  partner_name: string;
  partner_id: number;
  partner_users: HistoryUser[];
};

export type HistoryUser = {
  name: string;
  email: string;
  status: number;
  opened_at: string | null;
};

export type MailStatus = MailDraft | MailScheduled | MailCompleted;
export type MailDraft = 0;
export type MailScheduled = 1;
export type MailCompleted = 2;
