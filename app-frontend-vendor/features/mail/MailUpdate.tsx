import { MailEdit } from "./components/MailEdit";
import { useEffect, useState } from "react";
import { ax } from "../../utils/axios";
import { useRouter } from "next/router";
import {
  Mail,
  MailRequest,
  sendDraftMail,
  updateDraftMail,
  updateScheduleMail,
} from "./mail-api";

export const MailUpdate = () => {
  const router = useRouter();
  const [mail, setMail] = useState<Mail>();
  const [loading, setLoading] = useState(true);
  useEffect(() => {
    if (!router.isReady || typeof router.query.id !== "string") {
      return;
    }
    (async () => {
      const response = await ax.get(`/api/v1/mail/${router.query.id}`);
      setMail(response.data);
      setLoading(false);
    })();
  }, [router, router.query]);

  const handleSend = async (mailRequest: MailRequest) => {
    if (typeof router.query.id !== "string") return;
    const mailId = Number(router.query.id);
    if (mailRequest.datetime) {
      await updateScheduleMail(mailId, mailRequest);
      return;
    }
    await sendDraftMail(mailId, mailRequest);
  };

  const handleDraft = async (mailRequest: MailRequest) => {
    if (typeof router.query.id !== "string") return;
    const mailId = Number(router.query.id);
    await updateDraftMail(mailId, mailRequest);
  };

  return (
    <MailEdit
      mail={mail}
      loading={loading}
      handleSend={handleSend}
      handleDraft={handleDraft}
    />
  );
};
