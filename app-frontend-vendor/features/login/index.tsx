import styled from "@emotion/styled";
import { But<PERSON> } from "@mantine/core";
import { notifications } from "@mantine/notifications";
import type { AxiosError } from "axios";
import { PropNormal } from "components/svgs/logo/prop";
import { APIStatusCode } from "constants/api_status_code";
import { COOKIE_MAX_AGE } from "constants/commonSetting";
import Head from "next/head";
import { useRouter } from "next/router";
import { setCookie } from "nookies";
import IconNotiFailed from "public/icons/icon-noti-failed.svg";
import { type FormEvent, useEffect, useState } from "react";
import {
  type FieldErrors,
  type UseFormRegister,
  useForm,
} from "react-hook-form";
import { propcolors } from "styles/colors";
import { ax } from "utils/axios";
import {
  COMMON_ERROR_MESSAGES,
  COMMON_ERROR_TITLES,
} from "utils/values/errorMessages";

interface LoginFormValues {
  email: string;
  password: string;
}

interface PresentationProps {
  className?: string;
  submitFunc: (e?: FormEvent<HTMLFormElement>) => Promise<void>;
  register: UseFormRegister<LoginFormValues>;
  isSubmitting: boolean;
  errors: FieldErrors<LoginFormValues>;
}

const Presentation: React.FC<PresentationProps> = ({
  className,
  submitFunc,
  isSubmitting,
  register,
}) => (
  <>
    <Head>
      <title>ログイン | PartnerProp</title>
      {/* PROP-4056 optemoのタグをheadタグ内に入るようにする */}
      <script
        defer
        src="https://dashboard.optemo.jp/js/connect.js?id=partner-prop"
      />
    </Head>
    <main className={className}>
      <section id="login-wrapper">
        <div className="login-heading">
          <div className="proplogo">
            <PropNormal className="proplogo-icon" />
            <div className="proplogo-variant_group">
              <span className="proplogo-variant-light">for</span>
              <span className="proplogo-variant-bold">Vendor</span>
            </div>
          </div>
          <p className="login-heading-dialog">ログインしてください</p>
        </div>
        <form className="login-form" onSubmit={submitFunc}>
          <input
            type="text"
            placeholder="メールアドレス"
            {...register("email", { required: true })}
          />
          <input
            type="password"
            placeholder="パスワード"
            {...register("password", { required: true })}
          />
          <Button
            type="submit"
            className="type-proceed"
            fullWidth
            radius="md"
            size="md"
            loading={isSubmitting}
            disabled={isSubmitting}
            styles={{
              root: {
                marginTop: 8,
                backgroundColor: propcolors.red[700],
                fontWeight: 600,
                fontSize: 14,
                "&:hover": { backgroundColor: propcolors.red[600] },
              },
            }}
          >
            ログイン
          </Button>
        </form>
      </section>
    </main>
  </>
);

/* ---------------------------------------------------------- */
/* Styled Component                                           */
/* ---------------------------------------------------------- */
const Styled = styled(Presentation)`
  height: 100vh;
  width: 100vw;
  display: flex;
  align-items: center;
  justify-content: center;

  #login-wrapper {
    display: flex;
    flex-flow: column;
    align-items: center;
    justify-content: center;
    padding: 40px;
    background-color: ${propcolors.white};
    box-shadow: 0px 0px 16px 0px rgba(221, 221, 221, 1);
    border-radius: 16px;
  }
  @media screen and (max-width: 512px) {
    #login-wrapper {
      display: flex;
      flex-flow: column;
      align-items: center;
      justify-content: center;
      padding: 8vw 4vw;
      background-color: ${propcolors.white};
      border: 1px solid ${propcolors.gray[200]};
      border-radius: 10px;
    }
  }

  .proplogo {
    display: inline-flex;
    gap: 16px;
    align-items: center;
    &-icon {
      width: 200px;
    }
    &-variant-light {
      color: ${propcolors.red[700]};
      font-weight: 400;
      font-size: 20px;
      margin-right: 4px;
    }
    &-variant-bold {
      color: ${propcolors.red[700]};
      font-weight: 500;
      font-size: 20px;
    }
  }
  .login {
    &-heading {
      display: flex;
      flex-flow: column;
      align-items: center;
      gap: 24px;
      &-dialog {
        margin-bottom: 24px;
        font-size: 14px;
        color: ${propcolors.blackLight};
        font-weight: 500;
      }
    }
    &-form {
      display: flex;
      flex-flow: column;
      align-items: center;
      width: 100%;
      gap: 16px;
      input {
        width: 100%;
        padding: 12px;
        border-radius: 8px;
        max-height: 41px;
        background-color: ${propcolors.inputBackground};
        outline: none;
        border: 0px !important;
      }
      input::placeholder {
        color: ${propcolors.greyDefault};
        font-size: 14px;
      }
      &-actions {
        width: 100%;
        display: flex;
        align-items: center;
        justify-content: space-between;
        gap: 16px;
      }
      &-forgot {
        font-size: 14px;
      }
      button {
        width: 100%;
        padding: 9.5px 16px;
        border-radius: 8px;
        font-weight: 400 !important;
        margin-top: 8px;
        &.type-proceed {
          font-size: 14px;
          opacity: 1;
          font-weight: 600;
        }
      }
    }
    &-forgot-password {
      font-size: 14px;
      font-weight: 300;
      margin-top: 24px;
      color: ${propcolors.blackLight};
      text-decoration: none;
    }
  }
`;

export const LoginPage = () => {
  const router = useRouter();
  const { push, query, isReady } = router;
  const [isLoggedIn, setIsLoggedIn] = useState(false);

  const {
    register,
    handleSubmit,
    getValues,
    formState: { isSubmitting, errors },
  } = useForm<LoginFormValues>();

  const hasCookie = (name: string): boolean =>
    document.cookie.split(";").some((c) => c.trim().startsWith(`${name}=`));

  interface ApiError {
    error?: string;
    status_code?: APIStatusCode;
  }

  const onSubmit = async (values: LoginFormValues) => {
    await ax.get("/sanctum/csrf-cookie");

    try {
      const res = await ax.post("/login", values, {
        xsrfHeaderName: "X-XSRF-TOKEN",
      });

      if (res.data?.two_factor_required) {
        sessionStorage.setItem("PENDING_USER", JSON.stringify(res.data.user));
        await push({
          pathname: "/otp-input",
          query: {
            user_id: res.data.user_id,
            redirectURL: query.redirectURL ?? "",
          },
        });
        return;
      }

      if (!hasCookie("user")) {
        setCookie(null, "user", JSON.stringify(res.data), {
          maxAge: COOKIE_MAX_AGE,
          path: "/",
        });
      }

      setIsLoggedIn(true);
    } catch (err) {
      const error = err as AxiosError<ApiError>;
      const msg = error.response?.data?.error;
      const code = error.response?.data?.status_code;

      if (code === APIStatusCode.IP_RESTRICTED_ERROR) {
        notifications.show({
          title: COMMON_ERROR_TITLES.LOGIN_ERROR,
          message: COMMON_ERROR_MESSAGES.IP_RESTRICTED_ERROR,
          icon: <IconNotiFailed />,
          autoClose: 5000,
        });
      } else if (error.response?.status === 500) {
        notifications.show({
          title: COMMON_ERROR_TITLES.SERVER_ERROR,
          message: COMMON_ERROR_MESSAGES.SERVER_ERROR,
          icon: <IconNotiFailed />,
          autoClose: 5000,
        });
      } else {
        notifications.show({
          title: COMMON_ERROR_TITLES.LOGIN_ERROR,
          message: msg || COMMON_ERROR_MESSAGES.INVALID_CREDENTIALS,
          icon: <IconNotiFailed />,
          autoClose: 5000,
        });
      }
    }
  };

  useEffect(() => {
    if (!isReady || !isLoggedIn) return;

    const dest =
      typeof query.redirectURL === "string"
        ? decodeURIComponent(query.redirectURL)
        : "/partners";
    push(dest);
  }, [isLoggedIn, isReady, push, query.redirectURL]);

  useEffect(() => {
    const { email, password } = getValues();
    if ((email.trim() === "" || password.trim() === "") && isSubmitting) {
      notifications.show({
        title: "入力エラー",
        message: "メールアドレスおよびパスワードは必須です。",
        icon: <IconNotiFailed />,
        autoClose: 5000,
      });
    }
  }, [isSubmitting, getValues]);

  return (
    <Styled
      submitFunc={handleSubmit(onSubmit)}
      isSubmitting={isSubmitting}
      errors={errors}
      register={register}
    />
  );
};
