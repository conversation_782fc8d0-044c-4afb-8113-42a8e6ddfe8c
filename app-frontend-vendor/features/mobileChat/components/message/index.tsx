import styled from "@emotion/styled";
import {
  <PERSON>Download16<PERSON>illed,
  ArrowR<PERSON><PERSON>16<PERSON>illed,
  Delete16<PERSON>illed,
  Edit16Filled,
} from "@fluentui/react-icons";
import { ActionIcon, Button, Indicator } from "@mantine/core";
import { useRouter } from "next/router";
import { propcolors } from "styles/colors";
import { parseCookies } from "nookies";
import { useCallback, useEffect, useMemo, useState } from "react";
import { ax } from "utils/axios";
import { modals } from "@mantine/modals";
import saveAs from "file-saver";
import { MessageBox } from "components/form/message-box";
import { useMentionEditor } from "components/form/hooks/use-mention-editor";
import type { Editor } from "@tiptap/react";

type ChatMessageBlockProps = {
  message: chatMessage;
  mentionables: chatMentionableUser[] | null;
  mode: "channel" | "thread";
  mutate?: () => void;
  threadReplies?: chatMessage[];
  content: string;
};

type PresentationProps = {
  className?: string;
  editor: Editor | null;
  message: chatMessage;
  mode: "channel" | "thread";
  jumpThread: (id: number) => void;
  isVendor: boolean;
  userID: number;
  isReadOnly: boolean;
  toggleEditMode: () => void;
  resetValue: () => void;
  handleUpdateMessage: () => void;
  handleDeleteMessage: () => void;
  handleDeleteFile: (file_id: number) => void;
  downloadFile: (file: attached_files) => void;
  threadReplies?: chatMessage[];
};

const Presentation: React.FC<PresentationProps> = ({
  className,
  editor,
  message,
  jumpThread,
  isVendor,
  userID,
  mode,
  isReadOnly,
  toggleEditMode,
  resetValue,
  handleUpdateMessage,
  handleDeleteMessage,
  handleDeleteFile,
  downloadFile,
  threadReplies,
}) => {
  return (
    <div className={className} key={message.chat_post_id}>
      <div className="chat-heading">
        <section>
          <p className="chat-heading-sender">
            <span
              className={
                message.user.vendor_user_id !== null
                  ? "chat-heading-sender_icon"
                  : "chat-heading-sender_icon partner"
              }
            >
              {message.user.name.slice(0, 2)}
            </span>
            {message.user.name}
          </p>
          <p className="chat-heading-timestamp">
            {message.timestamp.created_at}
          </p>
        </section>
        <div className="chat-heading-buttons">
          {message.deleted !== 1 && (
            <>
              {userID ===
                (isVendor
                  ? message.user.vendor_user_id
                  : message.user.partner_user_id) && (
                <ActionIcon onClick={handleDeleteMessage}>
                  <Delete16Filled />
                </ActionIcon>
              )}
              {userID ===
                (isVendor
                  ? message.user.vendor_user_id
                  : message.user.partner_user_id) && (
                <ActionIcon onClick={toggleEditMode}>
                  <Edit16Filled />
                </ActionIcon>
              )}
              {mode === "channel" && (
                <Indicator
                  inline
                  label={threadReplies?.length}
                  size={threadReplies && threadReplies.length > 0 ? 16 : 0}
                >
                  <ActionIcon onClick={() => jumpThread(message.chat_post_id)}>
                    <ArrowReply16Filled />
                  </ActionIcon>
                </Indicator>
              )}
            </>
          )}
        </div>
      </div>
      <MessageBox editor={editor} />
      {!isReadOnly && (
        <div className="chat-message-editor">
          <Button compact variant={"subtle"} onClick={resetValue}>
            キャンセル
          </Button>
          <Button compact onClick={handleUpdateMessage}>
            編集を保存
          </Button>
        </div>
      )}
      {message.attached_files && (
        <div className="chat-attachments">
          {message.attached_files.map((file, index) => (
            <div className="chat-attachments-item" key={index}>
              <span>{file.name}</span>
              <ActionIcon onClick={() => downloadFile(file)}>
                <ArrowDownload16Filled />
              </ActionIcon>
              {!isReadOnly && (
                <ActionIcon onClick={() => handleDeleteFile(file.id)}>
                  <Delete16Filled />
                </ActionIcon>
              )}
            </div>
          ))}
        </div>
      )}
    </div>
  );
};

const Styled = styled(Presentation)`
  padding: 0rem 1rem 1rem 1rem;
  &:hover {
    background-color: ${propcolors.gray[200]}80;
  }
  .chat {
    &-heading {
      padding: 1rem 0;
      border-bottom: 1px solid ${propcolors.gray[200]};
      margin-bottom: 1rem;
      display: flex;
      justify-content: space-between;
      align-items: center;
      &-sender {
        font-weight: bold;
        font-size: 0.875rem;

        &_icon {
          display: inline-block;
          width: 40px;
          height: 40px;
          border-radius: 20px;
          background-color: ${propcolors.red[700]};
          color: ${propcolors.white};
          text-align: center;
          line-height: 40px;
          margin-right: 12px;
          font-size: 1rem;

          &.partner {
            background-color: ${propcolors.gray[800]};
          }
        }
      }
      &-timestamp {
        margin-top: 0.3rem;
        font-size: 0.8rem;
        color: ${propcolors.gray[500]};
      }
      &-buttons {
        display: flex;
      }
    }
    &-message {
      textarea {
      }
      &__input {
        border-style: none !important;
        &:not([disabled]) {
          border-radius: 0.25rem;
          border: 1px solid ${propcolors.gray[400]} !important;
        }
      }
      &-editor {
        display: flex;
        justify-content: flex-end;
        margin-top: 1rem;
      }
    }
    &-attachments {
      display: grid;
      grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
      gap: 1rem;
      margin-top: 1rem;
      &-item {
        border: 1px solid ${propcolors.gray[200]};
        border-radius: 0.25rem;
        padding: 0.5rem;
        display: grid;
        grid-template-columns: 1fr auto auto;
        align-items: center;
        font-size: 0.8rem;
        background-color: white;
        span {
          width: 100%;
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
        }
      }
    }
  }
`;

export const ChatMessageBlock: React.FC<ChatMessageBlockProps> = ({
  message,
  mentionables,
  mode,
  mutate,
  threadReplies,
  content,
}) => {
  const [isReadOnly, setEditMode] = useState<boolean>(true);
  const { replace, query } = useRouter();
  const mentions = useMemo(
    () =>
      mentionables
        ? mentionables.map((user) => ({
            label: user.label,
            id: user.text,
          }))
        : [],
    [mentionables]
  );
  const { editor } = useMentionEditor({
    mentions,
    editable: !isReadOnly,
  });
  const userID = JSON.parse(parseCookies()["user"]).user_id;
  const isVendor =
    typeof window !== "undefined" && window.location.hostname.includes("vendor")
      ? true
      : false;
  const jumpThread = (id: number) => {
    replace(
      `/chat/${query.id}?threads=${id}${
        query.title && "&title=" + query.title + "&isAll=" + query.isAll
      }`
    );
  };

  useEffect(() => {
    editor?.commands.setMentionContent(content);
  }, [content, editor]);

  const toggleEditMode = useCallback(() => {
    setEditMode(!isReadOnly);
  }, []);

  const resetValue = () => {
    editor?.commands.setMentionContent(content);
    setEditMode(true);
  };

  const handleUpdateMessage = () => {
    const editValue = editor?.getText({ blockSeparator: "\n" });
    ax.put(`/api/v1/chat/messages/${message.chat_post_id}`, {
      content: editValue,
      parent_id: message.parent_id,
    });
    setEditMode(true);
  };

  const handleDeleteFile = (file_id: number) => {
    modals.openConfirmModal({
      title: "ファイルを削除しますか？",
      labels: {
        cancel: "キャンセル",
        confirm: "削除",
      },
      onConfirm: () => {
        ax.delete(`/api/v1/chat/attached_files/${file_id}`).then((res) => {
          if (mutate) {
            mutate();
          }
        });
        setEditMode(true);
      },
    });
  };

  const handleDeleteMessage = () => {
    modals.openConfirmModal({
      title:
        "メッセージを削除しますか？メッセージを削除すると、関連付けられている全てのファイルも同時に削除されます。この操作は元に戻せません。",
      labels: {
        cancel: "キャンセル",
        confirm: "削除",
      },
      onConfirm: () => {
        if (message.attached_files && message.attached_files.length > 0) {
          message.attached_files.map((file) => {
            ax.delete(`/api/v1/chat/attached_files/${file.id}`);
          });
        }
        ax.delete(`/api/v1/chat/messages/${message.chat_post_id}`).then(() => {
          editor?.commands.setMentionContent(
            "[このメッセージは削除されました]"
          );
        });
      },
    });
  };

  const downloadFile = (file: attached_files) => {
    ax.get(`/api/v1/chat/attached_files/download/${file.id}`, {
      responseType: "blob",
    }).then((res) => {
      let name = "";
      if (file.name) {
        name = file.name;
      }
      const blob = new Blob([res.data], {
        type: res.headers["content-type"],
      });
      saveAs(blob, name);
    });
  };

  return (
    <Styled
      editor={editor}
      message={message}
      jumpThread={jumpThread}
      isVendor={isVendor}
      userID={userID}
      mode={mode}
      toggleEditMode={toggleEditMode}
      isReadOnly={isReadOnly}
      resetValue={resetValue}
      handleUpdateMessage={handleUpdateMessage}
      handleDeleteMessage={handleDeleteMessage}
      handleDeleteFile={handleDeleteFile}
      downloadFile={downloadFile}
      threadReplies={threadReplies}
    />
  );
};
