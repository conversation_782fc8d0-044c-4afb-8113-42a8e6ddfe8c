import styled from "@emotion/styled";
import { useRouter } from "next/router";
import { propcolors } from "styles/colors";
import useSWRImmutable from "swr/immutable";
import { ax } from "utils/axios";
import { ChatMessageBlock } from "../message";
import Pusher from "pusher-js";
import { useEffect, useRef, useState } from "react";
import { parseCookies } from "nookies";
import { SendForm } from "../sendForm";
import { People24Filled } from "@fluentui/react-icons";
import { ActionIcon, Drawer, Skeleton } from "@mantine/core";
import { ChannelUser } from "../channelUser";
import { useDisclosure } from "@mantine/hooks";
import { MobileChatThreads } from "../threads";

const pusher = new Pusher(process.env.NEXT_PUBLIC_PUSHER_APP_KEY!, {
  cluster: process.env.NEXT_PUBLIC_PUSHER_CLUSTER!,
  channelAuthorization: {
    endpoint: `${process.env.NEXT_PUBLIC_API_HOST}/api/v1/chat/authChannel`,
    headers: {
      "X-XSRF-TOKEN": `${parseCookies()["XSRF-TOKEN"]}`,
    },
    transport: "ajax",
    customHandler: (params, callback) => {
      ax.post("/api/v1/chat/authChannel", {
        channel_name: params.channelName,
        socket_id: params.socketId,
      }).then((res) => callback(null, res.data));
    },
  },
});

type PresentationProps = {
  className?: string;
  messages: chatMessage[] | null;
  isMessageValidating: boolean;
  mentionables: chatMentionableUser[] | null;
  isMentionableValidating: boolean;
  messagesRef: React.RefObject<HTMLDivElement>;
  threads: string | null;
  newThreadMessage: chatMessage | null;
  mutateMessageList: () => void;
  channelName: string | string[];
  isAll: boolean;
  userListModalOpened: boolean;
  openUserListModal: () => void;
  closeUserListModal: () => void;
};

type ChatMessengerProps = {
  mutateChannelList: () => void;
};

const Presentation: React.FC<PresentationProps> = ({
  className,
  messages,
  isMessageValidating,
  mentionables,
  isMentionableValidating,
  messagesRef,
  threads,
  newThreadMessage,
  mutateMessageList,
  channelName,
  isAll,
  userListModalOpened,
  openUserListModal,
  closeUserListModal,
}) => {
  return (
    <div className={className}>
      <section className="messages-heading">
        <p># {channelName}</p>
        {!isAll && (
          <ActionIcon>
            <People24Filled onClick={openUserListModal} />
          </ActionIcon>
        )}
      </section>
      {!threads && (
        <section ref={messagesRef} className="messages-list">
          {isMessageValidating ? (
            <div className="messages-list nonactive">
              <Skeleton height={14} mb={"xl"} width="30%" />
              <Skeleton height={14} mb={"xs"} />
              <Skeleton height={14} mb={"xs"} />
              <Skeleton height={14} mb={"xs"} width="60%" />
              <Skeleton height={8} mb={"md"} width="20%" />
              <Skeleton height={40} circle mb={"xs"} />
              <Skeleton height={14} mb={"xl"} width="30%" />
              <Skeleton height={14} mb={"xs"} />
              <Skeleton height={14} mb={"xs"} />
              <Skeleton height={14} mb={"xs"} width="60%" />
              <Skeleton height={8} mb={"md"} width="20%" />
              <Skeleton height={40} circle mb={"xs"} />
            </div>
          ) : (
            <>
              {messages && messages.length === 0 ? (
                <div className="messages-list nonactive">
                  表示するメッセージがありません。
                </div>
              ) : (
                messages &&
                messages
                  .filter((message) => message.parent_id === null)
                  .map((msg: chatMessage, index) => (
                    <ChatMessageBlock
                      key={msg.chat_post_id}
                      message={msg}
                      content={msg.content}
                      mentionables={mentionables}
                      mode={"channel"}
                      mutate={mutateMessageList}
                      threadReplies={messages.filter(
                        (message) => message.parent_id === msg.chat_post_id
                      )}
                    />
                  ))
              )}
            </>
          )}
        </section>
      )}
      {threads && (
        <MobileChatThreads
          mentionables={mentionables}
          mode="thread"
          newThreadMessage={newThreadMessage}
        />
      )}
      <SendForm
        mentionables={mentionables}
        mode="channel"
        mutate={mutateMessageList}
        key={"channel"}
      />
      <Drawer
        opened={userListModalOpened}
        onClose={closeUserListModal}
        position="right"
        size="md"
        title="チャンネルユーザー"
      >
        <ChannelUser />
      </Drawer>
    </div>
  );
};

const Styled = styled(Presentation)`
  display: grid;
  grid-template-rows: auto 1fr auto;
  height: 90vh;
  position: relative;
  overflow: hidden;
  @media (max-width: 512px) {
    height: calc(100dvh - 56px);
  }
  .messages {
    &-heading {
      display: fixed;
      top: 0;
      width: 100%;
      display: grid;
      grid-template-columns: 1fr auto;
      padding: 0.5rem 1rem;
      border-bottom: 1px solid ${propcolors.gray[200]};
      height: fit-content;
      p {
        font-size: 1rem;
        font-weight: bold;
        width: 100%;
        overflow: hidden;
        white-space: nowrap;
        text-overflow: ellipsis;
      }
    }
    &-list {
      display: flex;
      overflow-y: scroll;
      height: 100%;
      flex-direction: column-reverse;
      background-color: ${propcolors.white};
      &.nonactive {
        margin-left: 1rem;
      }
    }
    &-item {
    }
  }
`;

export const MobileChatMessenger: React.FC<ChatMessengerProps> = ({
  mutateChannelList,
}) => {
  const [message, setMessage] = useState<chatMessage[] | null>(null);
  const [newThreadMessage, setNewThreadMessage] = useState<chatMessage | null>(
    null
  );
  const { query } = useRouter();
  const { id, threads } = query;
  const [
    userListModalOpened,
    { open: openUserListModal, close: closeUserListModal },
  ] = useDisclosure(false);

  const messagesRef = useRef<HTMLDivElement>(null);

  const fetcher = (url: string) => ax.get(url).then((res) => res.data);

  const {
    data: messages,
    mutate: mutateMessageList,
    isValidating: isMessageValidating,
  } = useSWRImmutable(`api/v1/chat/${id ? id : 0}/messages`, fetcher);

  const {
    data: mentionables,
    mutate: mutateMentionables,
    isValidating: isMentionableValidating,
  } = useSWRImmutable(`api/v1/chat/${id ? id : 0}/mentionables`, fetcher);

  useEffect(() => {
    setMessage(messages);
  }, [messages]);

  useEffect(() => {
    if (id) {
      // ここ絶対リファクタした方がいい処理、もっと効率的に過去のid参照してください
      if (
        message &&
        message.length > 0 &&
        message[0].channel_id !== Number(id)
      ) {
        mutateMentionables();
        mutateMessageList();
      }
      ax.put(`api/v1/chat/${id}/read`);
      mutateChannelList();
    }
  }, [id]);

  useEffect(() => {}, []);

  useEffect(() => {
    const channel = pusher.subscribe(`private-channels.${id}`);
    channel.bind("pusher:subscription_error", (err: any) => console.log(err));
    channel.bind("message.created", (data: chatMessage) => {
      if (data.parent_id !== null) {
        setNewThreadMessage(data);
      }
      setMessage((prev) => [data, ...(prev ?? [])]);
    });
    channel.bind("message.deleted", (data: chatMessage) => {
      if (data.parent_id === null) {
        setMessage((prev) => {
          if (prev) {
            return prev.map((message) => {
              if (message.chat_post_id === data.chat_post_id) {
                message.deleted = 1;
                message.attached_files = [];
              }
              return message;
            });
          } else {
            return [];
          }
        });
      } else {
        setNewThreadMessage({ ...data, isDeleted: true });
      }
    });
    channel.bind("message.updated", (data: chatMessage) => {
      if (data.parent_id === null) {
        setMessage((prev) => {
          if (prev) {
            return prev.map((message) => {
              if (message.chat_post_id === data.chat_post_id) {
                message.content = data.content;
              }
              return message;
            });
          } else {
            return [];
          }
        });
      } else {
        setNewThreadMessage({ ...data, isUpdated: true });
      }
    });
    return () => {
      channel.unbind_all();
      channel.unsubscribe();
    };
  }, []);

  return (
    <Styled
      messages={message}
      isMessageValidating={isMessageValidating}
      mentionables={mentionables}
      isMentionableValidating={isMentionableValidating}
      messagesRef={messagesRef}
      threads={threads ? threads.toString() : null}
      newThreadMessage={newThreadMessage}
      mutateMessageList={mutateMessageList}
      channelName={query.title ? query.title : ""}
      isAll={query.isAll ? query.isAll === "true" : false}
      userListModalOpened={userListModalOpened}
      openUserListModal={openUserListModal}
      closeUserListModal={closeUserListModal}
    />
  );
};
