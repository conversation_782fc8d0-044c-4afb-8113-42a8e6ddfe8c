import styled from "@emotion/styled";
import { propcolors } from "styles/colors";
import { useMemo, useState } from "react";
import { <PERSON><PERSON><PERSON>, <PERSON><PERSON>, Drawer } from "@mantine/core";
import {
  Attach16Filled,
  Dism<PERSON>16Filled,
  Send20Filled,
} from "@fluentui/react-icons";
import { ax } from "utils/axios";
import { useRouter } from "next/router";
import { useDisclosure } from "@mantine/hooks";
import { AssetUpload } from "./components/assetUpload";
import { convertFileToBase64 } from "utils/func/base64Converter";
import { notifications } from "@mantine/notifications";
import useSWRImmutable from "swr/immutable";
import { MessageBox } from "components/form/message-box";
import { useMentionEditor } from "components/form/hooks/use-mention-editor";
import type { Editor } from "@tiptap/react";

type sendFormProps = {
  mentionables: chatMentionableUser[] | null;
  mode: "channel" | "thread";
  mutate?: () => void;
};

type PresentationProps = {
  className?: string;
  editor: Editor | null;
  attachments: any[] | null;
  handleSend: () => void;
  assetModalOpened: boolean;
  openAssetModal: () => void;
  closeAssetModal: () => void;
  handleAssetUpload: (file: File) => void;
  handleDeleteAttachment: (index: number) => void;
  isAll: boolean;
};

const Presentation: React.FC<PresentationProps> = ({
  className,
  editor,
  attachments,
  handleSend,
  assetModalOpened,
  openAssetModal,
  closeAssetModal,
  handleAssetUpload,
  handleDeleteAttachment,
  isAll,
}) => {
  return (
    <div className={className}>
      <>
        <MessageBox editor={editor} />
        {attachments && attachments.length > 0 && (
          <div className="sender-attachments">
            {attachments.map((file, index) => (
              <div className="sender-attachments-file" key={index}>
                <span>{file.name}</span>
                <ActionIcon onClick={() => handleDeleteAttachment(index)}>
                  <Dismiss16Filled />
                </ActionIcon>
              </div>
            ))}
          </div>
        )}
        <div className="sender-footer">
          <div className="sender-buttons">
            <Button
              leftIcon={<Attach16Filled />}
              compact
              variant="subtle"
              disabled={isAll}
              onClick={openAssetModal}
            >
              ファイルを添付...
            </Button>
          </div>
          <Button
            onClick={handleSend}
            rightIcon={<Send20Filled />}
            disabled={isAll}
          >
            送信
          </Button>
        </div>
      </>
      <Drawer
        opened={assetModalOpened}
        onClose={closeAssetModal}
        position="right"
        size="md"
        title="ファイルを添付"
      >
        <AssetUpload setAssetList={handleAssetUpload} />
      </Drawer>
    </div>
  );
};

const Styled = styled(Presentation)`
  border: 1px solid ${propcolors.gray[200]};
  display: grid;
  grid-template-rows: 1fr auto auto;
  align-items: center;
  border-radius: 0.5rem;
  margin-top: 1rem;
  background-color: ${propcolors.white};
  filter: drop-shadow(0 0 4px ${propcolors.gray[300]}30);
  @media screen and (max-width: 512px) {
    margin-top: 0;
  }
  .sender {
    &-inputForm {
      padding: 1rem;
      border-bottom: 1px solid ${propcolors.gray[200]};
      & .pulse {
        animation: pulse 0.85s infinite;
        @keyframes pulse {
          0% {
            opacity: 0;
          }
          50% {
            opacity: 1;
          }
          100% {
            opacity: 0;
          }
        }
      }
    }
    &-input {
      resize: none;
      height: 100%;
      padding: 1rem;
      max-height: 200px;
      font-size: 0.875rem;
      &__highlighter {
        border-style: none;
        border: 0;
      }
      &__input {
        border-style: none !important;
        resize: none;
        height: 100%;
        padding: 1rem;
        max-height: 200px;
        font-size: 0.875rem;
        overflow-y: auto !important;
      }
      &__suggestions {
        border: 1px solid ${propcolors.gray[200]};
        border-radius: 0.5rem;
        background-color: ${propcolors.white};
        top: 0 !important;
        margin: 0 !important;
        bottom: 30px;
        height: 100px;
        overflow-y: scroll;
        filter: drop-shadow(0 0 4px ${propcolors.gray[300]}30);
        font-size: 0.875rem;
        &__list {
          padding: 0;
          margin: 0;
          list-style-type: none;
        }
        &__item {
          padding: 0.5rem;
          font-size: 0.875rem;
          &--focused {
            background-color: ${propcolors.gray[200]};
          }
        }
      }
    }
    &-attachments {
      display: grid;
      grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
      align-items: center;
      padding: 1rem;
      font-size: 0.875rem;
      &-file {
        display: flex;
        align-items: center;
        justify-content: space-between;
        padding: 0.5rem;
        border-radius: 0.5rem;
        border: 1px solid ${propcolors.gray[200]};
        span {
          width: 100%;
          overflow: hidden;
          white-space: nowrap;
          text-overflow: ellipsis;
        }
      }
    }
    &-footer {
      display: grid;
      grid-template-columns: 1fr auto;
      align-items: center;
      font-size: 0.875rem;
      padding: 0.5rem 0.5rem;
      border-top: 1px solid ${propcolors.gray[200]};
    }
    &-buttons {
    }
  }
  .asset-modal {
    display: grid;
    gap: 1rem;
  }
`;

export const SendForm: React.FC<sendFormProps> = ({
  mentionables,
  mode,
  mutate,
}) => {
  const [assetModalOpened, { open: openAssetModal, close: closeAssetModal }] =
    useDisclosure(false);
  const [attachments, setAttachments] = useState<File[] | null>(null);
  const mentions = useMemo(
    () =>
      mentionables
        ? mentionables.map((user) => ({
            label: user.label,
            id: user.text,
          }))
        : [],
    [mentionables]
  );
  const { editor } = useMentionEditor({ mentions });
  const { query } = useRouter();

  const fetcher = (url: string) => ax.get(url).then((res) => res.data);
  const {
    data: channels,
    mutate: mutateChannelList,
    isValidating: isChannelValidating,
  } = useSWRImmutable(`api/v1/chat/channels`, fetcher);
  const handleSend = async () => {
    const value = editor?.getText({ blockSeparator: "\n" });
    if (editor?.isEmpty || !value) {
      return;
    }
    if (value.length > 3000) {
      notifications.show({
        title:
          "メッセージの送信に失敗しました。3000文字以上の文章を送ることはできません。",
        message: `（現在の文字数：${value.length}文字）`,
        color: "red",
        autoClose: 5000,
      });
      return;
    }
    const attachedFiles =
      attachments &&
      (await Promise.all(
        attachments.map(async (file) => {
          const new_base64_string = (await convertFileToBase64(file)) as string;
          return {
            name: file.name,
            base64_string: new_base64_string.slice(
              new_base64_string.indexOf(",") + 1
            ),
          };
        })
      ));
    await ax.post(`/api/v1/chat/${query.id}/messages`, {
      content: value,
      parent_id: Number(query.threads) ?? null,
      attached_files: attachedFiles,
    });
    editor?.commands.setMentionContent("");
    setAttachments(null);
  };
  const handleAssetUpload = (file: File) => {
    setAttachments((prev) => (prev ? [...prev, file] : [file]));
    closeAssetModal();
  };
  const handleDeleteAttachment = (index: number) => {
    setAttachments((prev) => prev?.filter((_, i) => i !== index) ?? null);
  };
  return (
    <Styled
      editor={editor}
      attachments={attachments}
      handleSend={handleSend}
      assetModalOpened={assetModalOpened}
      openAssetModal={openAssetModal}
      closeAssetModal={closeAssetModal}
      handleAssetUpload={handleAssetUpload}
      handleDeleteAttachment={handleDeleteAttachment}
      isAll={query.isAll ? query.isAll === "true" : false}
    />
  );
};
