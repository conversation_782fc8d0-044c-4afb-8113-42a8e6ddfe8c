import styled from "@emotion/styled";
import { propcolors } from "styles/colors";
import { useEffect, useState } from "react";
import { ActionIcon, Skeleton } from "@mantine/core";
import { Dismiss16Filled } from "@fluentui/react-icons";
import { ax } from "utils/axios";
import { SendForm } from "../sendForm";
import { useRouter } from "next/router";
import useSWRImmutable from "swr/immutable";
import { ChatMessageBlock } from "../message";

type ChatThreadProps = {
  mentionables: chatMentionableUser[] | null;
  mode: "channel" | "thread";
  newThreadMessage?: chatMessage | null;
};

type PresentationProps = {
  className?: string;
  closeThread: () => void;
  messages: chatMessage[] | null;
  isMessageValidating: boolean;
  mutate: () => void;
} & ChatThreadProps;

const Presentation: React.FC<PresentationProps> = ({
  className,
  mentionables,
  closeThread,
  messages,
  isMessageValidating,
  mutate,
  mode,
}) => {
  return (
    <div className={className}>
      <div className="thread-header">
        <p className="thread-header-title">スレッド</p>
        <ActionIcon onClick={closeThread}>
          <Dismiss16Filled />
        </ActionIcon>
      </div>
      <section className="thread-list ">
        {isMessageValidating ? (
          <div className="thread-list nonactive">
            <Skeleton height={14} mb={"xl"} width="30%" />
            <Skeleton height={14} mb={"xs"} />
            <Skeleton height={14} mb={"xs"} />
            <Skeleton height={14} mb={"xs"} width="60%" />
            <Skeleton height={8} mb={"md"} width="20%" />
            <Skeleton height={40} circle mb={"xs"} />
            <Skeleton height={14} mb={"xl"} width="30%" />
            <Skeleton height={14} mb={"xs"} />
            <Skeleton height={14} mb={"xs"} />
            <Skeleton height={14} mb={"xs"} width="60%" />
            <Skeleton height={8} mb={"md"} width="20%" />
            <Skeleton height={40} circle mb={"xs"} />
          </div>
        ) : (
          <>
            {messages?.map((msg: chatMessage, index) => (
              <ChatMessageBlock
                key={msg.chat_post_id}
                message={msg}
                content={msg.content}
                mentionables={mentionables}
                mode={mode}
                mutate={mutate}
              />
            ))}
          </>
        )}
      </section>
      <SendForm
        mentionables={mentionables}
        mode="thread"
        mutate={mutate}
        key={"thread"}
      />
    </div>
  );
};

const Styled = styled(Presentation)`
  position: absolute;
  top: 0;
  right: 0;
  background: white;
  width: 60%;
  height: 100%;
  border: 1px solid ${propcolors.gray[200]};
  z-index: 99;
  display: grid;
  grid-template-rows: auto 1fr auto;
  @media screen and (max-width: 512px) {
    width: 100%;
  }
  .thread {
    &-header {
      border-bottom: 1px solid ${propcolors.gray[200]};
      padding: 0.5rem 1rem;
      display: flex;
      justify-content: space-between;
      align-items: center;
    }
    &-list {
      display: flex;
      overflow-y: scroll;
      height: 100%;
      flex-direction: column-reverse;
      &.nonactive {
        margin-left: 1rem;
      }
    }
  }
`;

export const MobileChatThreads: React.FC<ChatThreadProps> = ({
  mentionables,
  mode,
  newThreadMessage,
}) => {
  const [threadMessages, setThreadMessages] = useState<chatMessage[]>([]);
  const { replace, query } = useRouter();
  const closeThread = () => {
    replace(
      `/chat/${query.id}${
        query.title && "?title=" + query.title + "&isAll=" + query.isAll
      }`
    );
  };
  const fetcher = (url: string) => ax.get(url).then((res) => res.data);
  const {
    data: messages,
    mutate: mutateMessageList,
    isValidating: isMessageValidating,
  } = useSWRImmutable(`api/v1/chat/thread/${query.threads}`, fetcher);

  useEffect(() => {
    if (query.threads) {
      mutateMessageList();
    }
  }, [query.threads]);

  useEffect(() => {
    if (messages) {
      setThreadMessages(messages);
    }
  }, [messages]);

  useEffect(() => {
    if (newThreadMessage) {
      if (newThreadMessage.isDeleted) {
        setThreadMessages((prev) => {
          if (prev) {
            return prev.map((message) => {
              if (message.chat_post_id === newThreadMessage.chat_post_id) {
                message.deleted = 1;
                message.attached_files = [];
              }
              return message;
            });
          } else {
            return [];
          }
        });
      } else if (newThreadMessage.isUpdated) {
        setThreadMessages((prev) => {
          if (prev) {
            return prev.map((message) => {
              if (message.chat_post_id === newThreadMessage.chat_post_id) {
                message.content = newThreadMessage.content;
              }
              return message;
            });
          } else {
            return [];
          }
        });
      } else if (newThreadMessage.parent_id === Number(query.threads)) {
        setThreadMessages((prev) => [newThreadMessage, ...prev]);
      }
    }
  }, [newThreadMessage]);

  return (
    <Styled
      mentionables={mentionables}
      messages={threadMessages}
      isMessageValidating={isMessageValidating}
      mutate={mutateMessageList}
      mode={mode}
      closeThread={closeThread}
    />
  );
};
