import styled from "@emotion/styled";
import { propcolors } from "styles/colors";
import { useEffect, useState } from "react";
import { Tabs } from "@mantine/core";
import { ax } from "utils/axios";


import useSWRImmutable from "swr/immutable";
import { useRouter } from "next/router";

type ChannelUserProps = {};

type PresentationProps = {
  className?: string;
  vendorUserList: User[] | null;
  partnerUserList: PartnerUser[] | null;
  isChannelMembersValidating: boolean;
};

const Presentation: React.FC<PresentationProps> = ({
  className,
  vendorUserList,
  partnerUserList,
  isChannelMembersValidating,
}) => {
  return (
    <div className={className}>
      <Tabs defaultValue={"vendorUser"}>
        <Tabs.List>
          <Tabs.Tab value="vendorUser">ベンダーユーザー</Tabs.Tab>
          <Tabs.Tab value="partnerUser">パートナーユーザー</Tabs.Tab>
        </Tabs.List>

        <Tabs.Panel value="vendorUser">
          {!isChannelMembersValidating ? (
            vendorUserList && vendorUserList.length > 0 ? (
              vendorUserList.map((user, index) => (
                <div className="userList" key={index}>
                  <p className="name">{user.name}</p>
                  <p className="email">{user.email}</p>
                </div>
              ))
            ) : (
              <p className="name">
                このチャットにはユーザーが参加していません。
              </p>
            )
          ) : (
            <p className="name">ユーザー情報取得中です...</p>
          )}
        </Tabs.Panel>
        <Tabs.Panel value="partnerUser">
          {!isChannelMembersValidating ? (
            partnerUserList && partnerUserList.length > 0 ? (
              partnerUserList.map((user, index) => (
                <div className="userList" key={index}>
                  <p className="name">{user.name}</p>
                  <p className="email">{user.email}</p>
                </div>
              ))
            ) : (
              <p className="name">
                このチャットにはユーザーが参加していません。
              </p>
            )
          ) : (
            <p className="name">ユーザー情報取得中です...</p>
          )}
        </Tabs.Panel>
      </Tabs>
    </div>
  );
};

const Styled = styled(Presentation)`
  .userList {
    height: 60px;
    padding: 0 10px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    border-bottom: 1px solid ${propcolors.gray[300]};
    font-size: 14px;
    .name {
      font-weight: bold;
    }
  }
`;

export const ChannelUser: React.FC<ChannelUserProps> = ({}) => {
  const [vendorUserList, setVendorUserList] = useState<User[] | null>(null);
  const [partnerUserList, setPartnerUserList] = useState<PartnerUser[] | null>(
    null
  );
  const { query } = useRouter();
  const { id } = query;
  const fetcher = (url: string) =>
    ax
      .get(url)
      .then((res) => res.data)
      .catch((err) => console.log(err));
  const {
    data: channelMembers,
    mutate: mutateChannelMembers,
    isValidating: isChannelMembersValidating,
  } = useSWRImmutable(`api/v1/chat/${id}/members`, fetcher);

  useEffect(() => {
    if (channelMembers && channelMembers.length > 0) {
      setVendorUserList(
        channelMembers.filter(
          (channelMember: chatUser) => channelMember.vendor_user_id
        )
      );
      setPartnerUserList(
        channelMembers.filter(
          (channelMember: chatUser) => channelMember.partner_user_id
        )
      );
    }
  }, [channelMembers]);
  return (
    <Styled
      vendorUserList={vendorUserList}
      partnerUserList={partnerUserList}
      isChannelMembersValidating={isChannelMembersValidating}
    />
  );
};
