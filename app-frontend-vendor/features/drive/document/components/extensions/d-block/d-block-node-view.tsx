import {
  Ri<PERSON>ddLine,
  Ri<PERSON><PERSON><PERSON>,
  RiH2,
  RiH3,
  <PERSON><PERSON><PERSON><PERSON><PERSON>nordered,
  RiListOrdered,
  Ri<PERSON>mageLine,
  RiFileCopyLine,
  RiDeleteBinLine, RiDraggable,
  RiFileTextLine,
} from "@remixicon/react";
import {
  NodeViewContent,
  type NodeViewProps,
  NodeViewWrapper,
} from '@tiptap/react';
import type React from 'react';
import { useEffect, useMemo, useRef, useState } from 'react';
import { ax } from "../../../../../../utils/axios";
import { useRouter } from "next/router";
import { PageLinkDropdown } from "../../PageLinkDropdown";
import { propcolors } from "styles/colors";

type BlockType = 'paragraph' | 'h2' | 'h3' | 'bulletList' | 'orderedList' | 'image' | 'pageLink';

const BLOCK_TYPES: { type: BlockType; label: string; icon: React.ComponentType<any> }[] = [
  { type: 'paragraph', label: 'テキスト', icon: RiText },
  { type: 'h2', label: '大見出し', icon: RiH2 },
  { type: 'h3', label: '小見出し', icon: RiH3 },
  { type: 'bulletList', label: '箇条書きリスト', icon: RiListUnordered },
  { type: 'orderedList', label: '番号付きリスト', icon: RiListOrdered },
  { type: 'image', label: '画像', icon: RiImageLine },
  { type: 'pageLink', label: 'ページリンク', icon: RiFileTextLine },
];

export const DBlockNodeView: React.FC<NodeViewProps> = ({
  node,
  getPos,
  editor,
}) => {
  const [showMenu, setShowMenu] = useState(false);
  const [showDragMenu, setShowDragMenu] = useState(false);
  const [isDragging, setIsDragging] = useState(false);
  const [isDragOver, setIsDragOver] = useState(false);
  const [showPageLinkDropdown, setShowPageLinkDropdown] = useState(false);
  const [dropdownPosition, setDropdownPosition] = useState({ top: 0, left: 0 });
  const [showPlaceholder, setShowPlaceholder] = useState(false);
  const [editMode, setEditMode] = useState({ isEdit: false, pageLinkPos: null as number | null });
  const menuRef = useRef<HTMLDivElement>(null);
  const buttonRef = useRef<HTMLButtonElement>(null);
  const nodeRef = useRef<HTMLDivElement>(null);
  const dragMenuRef = useRef<HTMLDivElement>(null);
  const dragButtonRef = useRef<HTMLDivElement>(null);
  const router = useRouter();
  const { portal_id } = router.query;

  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      // Handle add menu
      if (
        menuRef.current &&
        !menuRef.current.contains(event.target as Node) &&
        !buttonRef.current?.contains(event.target as Node)
      ) {
        setShowMenu(false);
      }

      // Handle drag menu
      if (
        dragMenuRef.current &&
        !dragMenuRef.current.contains(event.target as Node) &&
        !dragButtonRef.current?.contains(event.target as Node)
      ) {
        setShowDragMenu(false);
      }
    };

    const handleOpenPageLinkDropdown = (event: CustomEvent) => {
      const { position, targetElement, targetElementClass, isEdit, currentPageLinkPos } = event.detail;

      // Check if this d-block is the target (either by element or class match)
      const isTarget = (targetElement && nodeRef.current && targetElement === nodeRef.current) ||
                      (targetElementClass && nodeRef.current?.className === targetElementClass);

      if (isTarget) {
        setDropdownPosition(position);
        setShowPageLinkDropdown(true);
        setShowPlaceholder(true);
        setShowMenu(false);

        // Set edit mode if this is an edit operation
        if (isEdit && currentPageLinkPos !== undefined) {
          setEditMode({ isEdit: true, pageLinkPos: currentPageLinkPos });
        } else {
          setEditMode({ isEdit: false, pageLinkPos: null });
        }
      }
    };

    document.addEventListener('mousedown', handleClickOutside);

    // Listen for the event on this specific d-block element
    if (nodeRef.current) {
      nodeRef.current.addEventListener('openPageLinkDropdown', handleOpenPageLinkDropdown as EventListener);
    }

    // Also listen for global events as fallback
    window.addEventListener('openPageLinkDropdown', handleOpenPageLinkDropdown as EventListener);

    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
      if (nodeRef.current) {
        nodeRef.current.removeEventListener('openPageLinkDropdown', handleOpenPageLinkDropdown as EventListener);
      }
      window.removeEventListener('openPageLinkDropdown', handleOpenPageLinkDropdown as EventListener);
    };
  }, [nodeRef.current]); // Add dependency to re-setup when nodeRef changes

  const createNodeAfter = (type: BlockType) => {
    const pos = getPos() + node.nodeSize;

    // Create content based on block type with improved focus management
    const createBlockContent = () => {
      switch (type) {
        case 'pageLink':
          // This will be handled by handlePageLinkClick
          return null;

        case 'bulletList':
          editor.chain().focus(pos).insertContentAt(pos, {
            type: 'dBlock',
            content: [{
              type: 'bulletList',
              content: [{ type: 'listItem', content: [{ type: 'paragraph' }] }],
            }],
          }).run();
          setShowMenu(false);
          break;

        case 'orderedList':
          editor.chain().focus(pos).insertContentAt(pos, {
            type: 'dBlock',
            content: [{
              type: 'orderedList',
              content: [{ type: 'listItem', content: [{ type: 'paragraph' }] }],
            }],
          }).run();
          setShowMenu(false);
          break;

        case 'h2':
          editor.chain().focus(pos).insertContentAt(pos, {
            type: 'dBlock',
            content: [{ type: 'heading', attrs: { level: 2 } }],
          }).run();
          setShowMenu(false);
          break;

        case 'h3':
          editor.chain().focus(pos).insertContentAt(pos, {
            type: 'dBlock',
            content: [{ type: 'heading', attrs: { level: 3 } }],
          }).run();
          setShowMenu(false);
          break;

        case 'image': {
          // Trigger file upload for image selection, use CustomImage UI for preview/caption
          const fileInput = document.createElement('input');
          fileInput.type = 'file';
          fileInput.accept = 'image/*';
          fileInput.onchange = async (event) => {
            const file = (event.target as HTMLInputElement).files?.[0];
            if (file) {
              // Validate file type
              if (!file.type.startsWith('image/')) {
                alert('Please select a valid image file.');
                return;
              }
              // Validate file size (max 5MB)
              const maxSize = 5 * 1024 * 1024; // 5MB
              if (file.size > maxSize) {
                alert('Image size should be less than 5MB.');
                return;
              }
              try {
                if (!portal_id) throw new Error('Missing portal_id');
                // Upload file to server
                const formData = new FormData();
                formData.append('type', 'draft');
                formData.append('portal_id', portal_id?.toString() || '');
                formData.append('caption', '');
                formData.append('file', file);
                // API returns document_file_id after upload
                const createRes = await ax.post(`/api/v1/drive/document`, formData);
                const document_file_id = createRes?.data;
                if (!document_file_id) throw new Error('No file id returned');
                // Step 2: Get S3 link after upload
                const linkRes = await ax.get(`/api/v1/drive/document/${document_file_id}/file`);
                const imageUrl = linkRes?.data;
                // Step 3: Insert customImage node using Tiptap command API (setCustomImage)
                const maxWidth = 600;
                const aspectRatio = 16 / 9;
                const calculatedHeight = maxWidth / aspectRatio;
                // Insert dBlock with customImage node inside
                editor.commands.insertContentAt(pos, {
                  type: 'dBlock',
                  content: [
                    {
                      type: 'customImage',
                      attrs: {
                        src: imageUrl,
                        alt: file.name,
                        caption: '',
                        width: maxWidth,
                        height: calculatedHeight,
                        "data-id": Number(document_file_id),
                      },
                    },
                  ],
                });
                setTimeout(() => {
                  try {
                    editor.commands.focus(pos + 2);
                  } catch (error) {
                    editor.commands.focus();
                  }
                }, 10);
              } catch (error) {
                alert('Failed to upload image. Please try again.');
              }
            }
          };
          fileInput.click();
          setShowMenu(false);
          break;
        }

        case 'paragraph':
        default:
          editor.chain().focus(pos).insertContentAt(pos, {
            type: 'dBlock',
            content: [{ type: 'paragraph', content: [] }],
          }).run();
          setShowMenu(false);
          break;
      }
    };

    // Insert the new block (logic is now handled inside createBlockContent)
    createBlockContent();

  };

  // Duplicate the current dBlock node at the next position
  const handleDuplicateBlock = () => {
    const pos = getPos() + node.nodeSize;
    // Clone node content and insert a new dBlock after current
    const clonedNode = node.copy(node.content);
    editor.commands.insertContentAt(pos, {
      type: 'dBlock',
      content: clonedNode.content.toJSON(),
    });
    setShowDragMenu(false);
  };

  // Delete the current dBlock node
  const handleDeleteBlock = () => {
    const pos = getPos();
    const nodeSize = node.nodeSize;
    // Use Tiptap command to delete the node range
    editor.commands.deleteRange({ from: pos, to: pos + nodeSize });
    setShowDragMenu(false);
  };

  const handlePageLinkSelect = (fileId: number, fileName: string, url: string) => {
    if (editMode.isEdit && editMode.pageLinkPos !== null) {
      // Edit mode: Replace existing page link at specific position
      const pageLinkPos = editMode.pageLinkPos;

      // Replace the page link node with new attributes
      editor.commands.insertContentAt(pageLinkPos, {
        type: 'pageLink',
        attrs: {
          fileId,
          fileName,
          url,
        },
      });

      // Delete the old page link (it will be after the new one)
      setTimeout(() => {
        editor.commands.deleteRange({
          from: pageLinkPos + 1,
          to: pageLinkPos + 2
        });
      }, 10);

      // Reset edit mode
      setEditMode({ isEdit: false, pageLinkPos: null });
    } else {
      // Normal mode: Replace current d-block content with page link
      const pos = getPos();
      const nodeSize = node.nodeSize;

      // Replace the entire current d-block with a new d-block containing page link
      editor.commands.deleteRange({ from: pos, to: pos + nodeSize });

      editor.commands.insertContentAt(pos, {
        type: 'dBlock',
        content: [
          {
            type: 'pageLink',
            attrs: {
              fileId,
              fileName,
              url,
            },
          },
        ],
      });
    }

    setShowPageLinkDropdown(false);
    setShowPlaceholder(false);
    setShowMenu(false);
  };

  const handlePageLinkClick = () => {
    // Get the position of the current d-block node instead of the button
    if (nodeRef.current) {
      const nodeRect = nodeRef.current.getBoundingClientRect();
      const viewportWidth = window.innerWidth;
      const dropdownWidth = 320; // Width of our dropdown

      // Center the dropdown horizontally relative to the viewport
      const centerLeft = (viewportWidth - dropdownWidth) / 2;

      setDropdownPosition({
        top: nodeRect.bottom + window.scrollY + 16,
        left: centerLeft,
      });
    }
    setShowPageLinkDropdown(true);
    setShowPlaceholder(true);
    setShowMenu(false);
    // Reset edit mode for insert operation
    setEditMode({ isEdit: false, pageLinkPos: null });
  };

  return (
    <NodeViewWrapper
      as="div"
      ref={nodeRef}
      className="group"
      draggable={true} // Enable default draggable behavior
      data-node-view-wrapper
      css={{
        display: 'flex',
        gap: '0.5rem',
        width: '100%',
        position: 'relative',
        opacity: isDragging ? 0.5 : 1,
        transition: 'all 0.2s ease',
        borderTop: isDragOver ? '2px solid #3b82f6' : '2px solid transparent',
        borderBottom: isDragOver ? '2px solid #3b82f6' : '2px solid transparent',
        backgroundColor: isDragOver ? 'rgba(59, 130, 246, 0.05)' : 'transparent',
        borderRadius: isDragOver ? '4px' : '0px',
      }}
    >
      <section
        aria-label="left-menu"
        css={{ display: 'flex', marginTop: '0.5rem', gap: '0.375rem' }}
      >
        <div css={{ position: 'relative' }}>
          <button
            ref={buttonRef}
            type="button"
            css={{
              opacity: 0,
              transition: 'opacity 0.2s ease, background-color 0.2s ease',
              padding: '4px',
              borderRadius: '4px',
              border: '1px solid #E8EAED',
              backgroundColor: 'transparent',
              cursor: 'pointer',
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              borderStyle: "none",
              width: "32px",
              height: "32px",

              '&:hover': {
                backgroundColor: 'rgba(0, 0, 0, 0.1)',
              },

              '.group:hover &': {
                opacity: 1,
              },

              '&:focus': {
                outline: '2px solid #3b82f6',
                outlineOffset: '1px',
                opacity: 1,
              },
            }}
            onClick={() => setShowMenu(!showMenu)}
            aria-label="新しいブロックを追加"
            aria-expanded={showMenu}
            aria-haspopup="true"
          >
            <RiAddLine
              css={{
                color: propcolors.greyDefault,
                transition: 'color 0.2s ease',
                fontSize: "16px",

                '&:hover': {
                  color: 'rgba(55, 65, 81, 1)',
                },
              }}
            />
          </button>

          {showMenu && (
            <div
              ref={menuRef}
              css={{
                position: 'absolute',
                left: 0,
                width: '13rem',
                backgroundColor: propcolors.white,
                borderRadius: '0.75rem',
                boxShadow: '0 4px 24px 0 rgba(0,0,0,0.10)',
                border: `1px solid ${propcolors.gray[200]}`,
                zIndex: 50,
                maxHeight: '20rem',
                overflowY: 'auto',
                overflowX: 'hidden',
                whiteSpace: 'normal',
                wordBreak: 'break-word',
                animation: 'fadeIn 0.15s ease-out',

                '@keyframes fadeIn': {
                  from: {
                    opacity: 0,
                    transform: 'translateY(-4px)',
                  },
                  to: {
                    opacity: 1,
                    transform: 'translateY(0)',
                  },
                },
              }}
              role="menu"
            >
              {BLOCK_TYPES.map(({ type, label, icon: Icon }) => (
                <button
                  key={type}
                  type="button"
                  css={{
                    width: "100%",
                    display: "flex",
                    alignItems: "center",
                    gap: "0.75rem",
                    textAlign: "left",
                    padding: "0.75rem 1rem",
                    fontSize: "0.95rem",
                    fontWeight: 500,
                    color: propcolors.gray[700],
                    backgroundColor: propcolors.white,
                    border: "none !important",
                    borderBottom: `1px solid ${propcolors.gray[200]} !important`,
                    borderRadius: 0,
                    cursor: "pointer",
                    transition: "background-color 0.15s ease, color 0.15s ease",
                    boxSizing: "border-box",

                    "&:last-of-type": {
                      borderBottom: "none !important",
                    },

                    "&:hover": {
                      backgroundColor: propcolors.gray[150],
                      color: propcolors.black,
                    },

                    "&:active": {
                      backgroundColor: propcolors.gray[200],
                    },

                    "&:focus": {
                      outline: `2px solid ${propcolors.blue[500]}`,
                      outlineOffset: "1px",
                      backgroundColor: propcolors.gray[150],
                    },
                  }}
                  onClick={() => type === 'pageLink' ? handlePageLinkClick() : createNodeAfter(type)}
                  role="menuitem"
                >
                  <Icon
                    css={{
                      width: "16px",
                      height: "16px",
                      color: propcolors.greyDefault,
                      flexShrink: 0,
                      fontSize: "1.25rem",
                      transition: "color 0.15s ease",
                    }}
                  />
                  <span
                    css={{
                      flex: 1,
                      fontWeight: 400,
                      color: propcolors.blackLight,
                      fontSize: "14px",
                      letterSpacing: "0.01em",
                    }}
                  >
                    {label}
                  </span>
                </button>
              ))}
            </div>
          )}
        </div>
        <div css={{ position: 'relative' }}>
          <div
            ref={dragButtonRef}
            css={{
              opacity: 0,
              transition: 'opacity 0.2s, background-color 0.2s',
              padding: '0.125rem',
              borderRadius: '0.25rem',
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              position: 'relative',
              '&:hover': {
                backgroundColor: 'rgba(0, 0, 0, 0.05)',
              },
              '.group:hover &': {
                opacity: 1,
              },
            }}
            contentEditable={false}
            aria-label="ブロックをドラッグまたはアクション"
            title="ドラッグしてブロックを移動、またはクリックでアクション"
          >
            {/* Single draggable element that contains the icon */}
            <div
              draggable={true} // Enable native drag for handle
              data-drag-handle="true"
              data-testid="drag-handle"
              css={{
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
                cursor: 'grab',
                padding: '2px',
                borderRadius: '2px',
                userSelect: 'none',
                border: '1px solid #E8EAED',
                '&:hover': {
                  backgroundColor: 'rgba(0, 0, 0, 0.1)',
                },
                '&:active': {
                  cursor: 'grabbing',
                },
                '&:focus': {
                  outline: '2px solid #3b82f6',
                  outlineOffset: '1px',
                },
              }}
              tabIndex={0}
              role="button"
              aria-label="ドラッグしてブロックを移動"
              onMouseDown={() => {
                // Don't prevent default here - let drag work naturally
              }}
              onMouseUp={() => {
              }}
              onClick={(e) => {
                // Only handle click if we're not in the middle of a drag
                e.stopPropagation();
                if (!isDragging) {
                  setShowDragMenu(!showDragMenu);
                }
              }}
            >
              <RiDraggable
                css={{
                  color: 'rgba(107, 114, 128, 1)',
                  transition: 'color 0.2s',
                  pointerEvents: 'none', // Let parent handle all events
                  '&:hover': {
                    color: 'rgba(75, 85, 99, 1)',
                  },
                }}
              />
            </div>
          </div>

          {showDragMenu && (
            <div
              ref={dragMenuRef}
              css={{
                position: 'absolute',
                left: 0,
                marginTop: '0.25rem',
                width: '8rem',
                backgroundColor: 'white',
                borderRadius: '0.5rem',
                boxShadow: '0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06)',
                border: '1px solid #E8EAED',
                zIndex: 50,
              }}
              role="menu"
            >
              <button
                type="button"
                css={{
                  width: '100%',
                  display: 'flex',
                  alignItems: 'center',
                  gap: '0.5rem',
                  textAlign: 'left',
                  padding: "10px",
                  fontSize: '0.875rem',
                  fontWeight: '400',
                  color: propcolors.blackLight,
                  backgroundColor: 'transparent',
                  border: 'none',
                  borderStyle: "none !important",
                  borderBottom: `1px solid ${propcolors.gray[200]} !important`,
                  cursor: 'pointer',
                  transition: 'all 0.15s ease-in-out',
                  '&:hover': {
                    backgroundColor: propcolors.greyBreadcrumb,
                  },
                }}
                onClick={handleDuplicateBlock}
                role="menuitem"
              >
                <RiFileCopyLine
                  css={{
                    width: '1rem',
                    height: '1rem',
                    color: propcolors.greyDefault,
                    flexShrink: 0,
                  }}
                />
                <span css={{fontSize: "14px"}}>複製</span>
              </button>

              <button
                type="button"
                css={{
                  width: '100%',
                  display: 'flex',
                  alignItems: 'center',
                  gap: '0.5rem',
                  textAlign: 'left',
                  fontSize: '0.95rem',
                  padding: "10px",
                  fontWeight: 500,
                  color: propcolors.blackLight,
                  backgroundColor: propcolors.white,
                  border: 'none',
                  borderStyle: "none !important",
                  cursor: 'pointer',
                  transition: 'all 0.15s cubic-bezier(.4,0,.2,1)',
                  position: 'relative',
                  boxSizing: 'border-box',
                  '&:hover': {
                    backgroundColor: propcolors.greyBreadcrumb,
                  },
                }}
                onClick={handleDeleteBlock}
                role="menuitem"
              >
                <RiDeleteBinLine
                  css={{
                    width: '1rem',
                    height: '1rem',
                    color: propcolors.greyDefault,
                    flexShrink: 0,
                  }}
                />
                <span css={{fontSize: "14px"}}>削除</span>
              </button>
            </div>
          )}
        </div>
      </section>
      <div style={{ position: 'relative', width: '100%' }}>
        <NodeViewContent
          css={{
            width: '100%',
          }}
        />
        {showPlaceholder && showPageLinkDropdown && !editMode.isEdit && (
          <div
            style={{
              position: 'absolute',
              top: '8px',
              left: '12px',
              right: 0,
              pointerEvents: 'none',
              color: '#9ca3af',
              fontSize: '16px',
              fontWeight: 400,
              zIndex: 1,
              lineHeight: '1.5',
            }}
          >
            ページリンクを挿入
          </div>
        )}
      </div>

      <PageLinkDropdown
        opened={showPageLinkDropdown}
        onClose={() => {
          setShowPageLinkDropdown(false);
          setShowPlaceholder(false);
        }}
        onSelect={handlePageLinkSelect}
        portalId={portal_id}
        position={dropdownPosition}
      />
    </NodeViewWrapper>
  );
};
