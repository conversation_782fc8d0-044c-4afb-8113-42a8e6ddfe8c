import {
  Ri<PERSON>ddLine,
  Ri<PERSON><PERSON><PERSON>,
  RiH2,
  <PERSON>iH<PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>nordered,
  RiListOrdered,
  Ri<PERSON>mageLine,
  RiFileLine,
  RiFileCopyLine,
  RiDeleteBinLine, RiDraggable,
} from "@remixicon/react";
import {
  NodeViewContent,
  type NodeViewProps,
  NodeViewWrapper,
} from '@tiptap/react';
import type React from 'react';
import { useEffect, useMemo, useRef, useState } from 'react';
import { ax } from "../../../../../../utils/axios";
import { useRouter } from "next/router";
import { PageLinkDropdown } from "../../PageLinkDropdown";

type BlockType = 'paragraph' | 'h2' | 'h3' | 'bulletList' | 'orderedList' | 'image' | 'pageLink';

const BLOCK_TYPES: { type: BlockType; label: string; icon: React.ComponentType<any> }[] = [
  { type: 'paragraph', label: 'テキスト', icon: RiText },
  { type: 'h2', label: '大見出し', icon: RiH2 },
  { type: 'h3', label: '小見出し', icon: RiH3 },
  { type: 'bulletList', label: '箇条書きリスト', icon: RiListUnordered },
  { type: 'orderedList', label: '番号付きリスト', icon: RiListOrdered },
  { type: 'image', label: '画像', icon: RiImageLine },
  { type: 'pageLink', label: 'ページリンク', icon: RiFileLine },
];

export const DBlockNodeView: React.FC<NodeViewProps> = ({
  node,
  getPos,
  editor,
}) => {
  const [showMenu, setShowMenu] = useState(false);
  const [showDragMenu, setShowDragMenu] = useState(false);
  const [isDragging, setIsDragging] = useState(false);
  const [isDragOver, setIsDragOver] = useState(false);
  const [showPageLinkDropdown, setShowPageLinkDropdown] = useState(false);
  const [dropdownPosition, setDropdownPosition] = useState({ top: 0, left: 0 });
  const [showPlaceholder, setShowPlaceholder] = useState(false);
  const menuRef = useRef<HTMLDivElement>(null);
  const buttonRef = useRef<HTMLButtonElement>(null);
  const nodeRef = useRef<HTMLDivElement>(null);
  const dragMenuRef = useRef<HTMLDivElement>(null);
  const dragButtonRef = useRef<HTMLDivElement>(null);
  const router = useRouter();
  const { portal_id } = router.query;

  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      // Handle add menu
      if (
        menuRef.current &&
        !menuRef.current.contains(event.target as Node) &&
        !buttonRef.current?.contains(event.target as Node)
      ) {
        setShowMenu(false);
      }

      // Handle drag menu
      if (
        dragMenuRef.current &&
        !dragMenuRef.current.contains(event.target as Node) &&
        !dragButtonRef.current?.contains(event.target as Node)
      ) {
        setShowDragMenu(false);
      }
    };

    const handleOpenPageLinkDropdown = (event: CustomEvent) => {
      const { position, targetElement } = event.detail;

      // Only handle if this d-block is the target
      if (targetElement && nodeRef.current && targetElement === nodeRef.current) {
        setDropdownPosition(position);
        setShowPageLinkDropdown(true);
        setShowPlaceholder(true);
        setShowMenu(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);

    // Listen for the event on this specific d-block element
    if (nodeRef.current) {
      nodeRef.current.addEventListener('openPageLinkDropdown', handleOpenPageLinkDropdown as EventListener);
    }

    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
      if (nodeRef.current) {
        nodeRef.current.removeEventListener('openPageLinkDropdown', handleOpenPageLinkDropdown as EventListener);
      }
    };
  }, [nodeRef.current]); // Add dependency to re-setup when nodeRef changes

  const createNodeAfter = (type: BlockType) => {
    const pos = getPos() + node.nodeSize;

    // Create content based on block type with improved focus management
    const createBlockContent = () => {
      switch (type) {
        case 'pageLink':
          // This will be handled by handlePageLinkClick
          return null;

        case 'bulletList':
        case 'orderedList':
          return {
            type: 'dBlock',
            content: [
              {
                type,
                content: [
                  {
                    type: 'listItem',
                    content: [{ type: 'paragraph' }],
                  },
                ],
              },
            ],
          };

        case 'h2':
        case 'h3':
          return {
            type: 'dBlock',
            content: [
              {
                type: 'heading',
                attrs: { level: type === 'h2' ? 2 : 3 },
              },
            ],
          };

        case 'image':
          // Trigger file upload for image selection
          const fileInput = document.createElement('input');
          fileInput.type = 'file';
          fileInput.accept = 'image/*';
          fileInput.onchange = async (event) => {
            const file = (event.target as HTMLInputElement).files?.[0];
            if (file) {
              // Validate file type
              if (!file.type.startsWith('image/')) {
                alert('Please select a valid image file.');
                return;
              }

              // Validate file size (max 5MB)
              const maxSize = 5 * 1024 * 1024; // 5MB
              if (file.size > maxSize) {
                alert('Image size should be less than 5MB.');
                return;
              }

              try {
                // Get portal_id from window (should be injected from main page)
                // You may want to improve this by passing via context/prop

                if (!portal_id) throw new Error('Missing portal_id');

                // Step 1: Call API to create new file and upload image (using portal_id)
                const formData = new FormData();
                formData.append('type', 'draft');
                formData.append('portal_id', portal_id?.toString() || '');
                formData.append('caption','');
                formData.append('file', file);
                // API returns document_file_id after upload
                const createRes = await ax.post(`/api/v1/drive/document`, formData);
                const document_file_id = createRes?.data;
                if (!document_file_id) throw new Error('No file id returned');
                // Step 2: Get S3 link after upload
                const linkRes = await ax.get(`/api/v1/drive/document/${document_file_id}/file`);
                const imageUrl = linkRes?.data;

                // Step 3: Insert custom image block
                const maxWidth = 600;
                const aspectRatio = 16 / 9;
                const calculatedHeight = maxWidth / aspectRatio;
                editor.commands.insertContentAt(pos, {
                  type: 'dBlock',
                  content: [
                    {
                      type: 'customImage',
                      attrs: {
                        src: imageUrl,
                        alt: file.name,
                        caption: '',
                        width: maxWidth,
                        height: calculatedHeight,
                        "data-id": Number(document_file_id)
                      },
                    },
                  ],
                });

                // Focus after the new block
                setTimeout(() => {
                  try {
                    editor.commands.focus(pos + 2);
                  } catch (error) {
                    editor.commands.focus();
                  }
                }, 10);
              } catch (error) {
                // eslint-disable-next-line no-console
                alert('Failed to upload image. Please try again.');
              }
            }
          };
          fileInput.click();

          // Return null to prevent default block creation
          return null;

        case 'paragraph':
        default:
          return {
            type: 'dBlock',
            content: [
              {
                type: 'paragraph',
                content: [],
              },
            ],
          };
      }
    };

    // Insert the new block
    const blockContent = createBlockContent();

    // Handle special case where image upload is triggered (returns null)
    if (blockContent === null) {
      setShowMenu(false);
      return;
    }

    const success = editor.commands.insertContentAt(pos, blockContent);

    if (success) {
      // Calculate focus position based on block type
      let focusPos = pos + 1; // Default position inside the new block

      if (type === 'bulletList' || type === 'orderedList') {
        focusPos = pos + 3; // Inside the list item paragraph
      }

      // Set focus with improved timing and error handling
      setTimeout(() => {
        try {
          editor.commands.focus(focusPos);
        } catch (error) {
          // Fallback: focus at the end of document
          editor.commands.focus();
        }
      }, 10);
    }

    setShowMenu(false);
  };

  const handleDuplicateBlock = () => {
    const pos = getPos() + node.nodeSize;

    // Clone the current node
    const clonedNode = node.copy(node.content);

    editor.commands.insertContentAt(pos, {
      type: 'dBlock',
      content: clonedNode.content.toJSON(),
    });

    setShowDragMenu(false);
  };

  const handleDeleteBlock = () => {
    const pos = getPos();
    const nodeSize = node.nodeSize;

    editor.commands.deleteRange({ from: pos, to: pos + nodeSize });
    setShowDragMenu(false);
  };

  const handlePageLinkSelect = (fileId: number, fileName: string, url: string) => {
    const pos = getPos() + node.nodeSize;

    // Insert page link block
    editor.commands.insertContentAt(pos, {
      type: 'dBlock',
      content: [
        {
          type: 'pageLink',
          attrs: {
            fileId,
            fileName,
            url,
          },
        },
      ],
    });

    // Create a new paragraph block after the page link
    setTimeout(() => {
      const nextPos = pos + 2;
      editor.commands.insertContentAt(nextPos, {
        type: 'dBlock',
        content: [{ type: 'paragraph', content: [] }],
      });
      editor.commands.focus(nextPos + 1);
    }, 10);

    setShowPageLinkDropdown(false);
    setShowPlaceholder(false);
    setShowMenu(false);
  };

  const handlePageLinkClick = () => {
    // Get the position of the current d-block node instead of the button
    if (nodeRef.current) {
      const nodeRect = nodeRef.current.getBoundingClientRect();
      const viewportWidth = window.innerWidth;
      const dropdownWidth = 320; // Width of our dropdown

      // Center the dropdown horizontally relative to the viewport
      const centerLeft = (viewportWidth - dropdownWidth) / 2;

      setDropdownPosition({
        top: nodeRect.bottom + window.scrollY + 16,
        left: centerLeft,
      });
    }
    setShowPageLinkDropdown(true);
    setShowPlaceholder(true);
    setShowMenu(false);
  };

  // Drag & Drop handlers
  const handleDragStart = (event: React.DragEvent) => {
    setIsDragging(true);
    setShowDragMenu(false);

    const pos = getPos();

    // Store the position for ProseMirror drag handling
    const dragData = {
      type: 'dBlock',
      pos,
      nodeSize: node.nodeSize,
      nodeType: node.type.name,
      content: node.toJSON()
    };

    event.dataTransfer.setData('application/json', JSON.stringify(dragData));
    event.dataTransfer.setData('text/plain', 'dBlock'); // Fallback
    event.dataTransfer.effectAllowed = 'move';

    // Create a simple drag image
    const dragImage = document.createElement('div');
    dragImage.innerHTML = '📄 Moving Block';
    dragImage.style.cssText = `
      position: absolute;
      top: -1000px;
      left: -1000px;
      background: rgba(59, 130, 246, 0.9);
      color: white;
      border-radius: 6px;
      padding: 8px 12px;
      font-size: 14px;
      font-weight: 500;
      box-shadow: 0 4px 12px rgba(0,0,0,0.2);
      pointer-events: none;
      z-index: 9999;
    `;
    document.body.appendChild(dragImage);
    event.dataTransfer.setDragImage(dragImage, 50, 20);

    // Clean up drag image
    setTimeout(() => {
      if (document.body.contains(dragImage)) {
        document.body.removeChild(dragImage);
      }
    }, 100);

    // Prevent ProseMirror from interfering
    event.stopPropagation();
  };

  const handleDragEnd = () => {
    setIsDragging(false);
  };

  const handleDragOver = (event: React.DragEvent) => {
    event.preventDefault();
    event.dataTransfer.dropEffect = 'move';

    // Set drag over state for visual feedback
    // Note: getData() might not work in dragover, so we'll rely on drag/drop events
    setIsDragOver(true);
  };

  const handleDragLeave = (event: React.DragEvent) => {
    // Only set to false if we're actually leaving this element
    if (!event.currentTarget.contains(event.relatedTarget as Node)) {
      setIsDragOver(false);
    }
  };

  const handleDrop = (event: React.DragEvent) => {
    event.preventDefault();
    event.stopPropagation();

    try {
      // Try to get drag data
      let dragDataString = event.dataTransfer.getData('application/json');
      if (!dragDataString) {
        // Fallback to text/plain
        const textData = event.dataTransfer.getData('text/plain');
        if (textData === 'dBlock') {
          console.log('Found dBlock via text/plain fallback');
        } else {
          return;
        }
      }

      if (dragDataString) {
        const dragData = JSON.parse(dragDataString);

        if (dragData.type !== 'dBlock') {
          return;
        }

        const dropPos = getPos();
        const sourcePos = dragData.pos;


        // Don't drop on itself
        if (sourcePos === dropPos) {
          return;
        }

        // Use ProseMirror transaction for safe node movement
        const { state } = editor;
        const { tr, doc } = state;

        // Validate positions
        if (sourcePos < 0 || sourcePos >= doc.content.size || dropPos < 0 || dropPos >= doc.content.size) {
          return;
        }

        // Get the source node
        const sourceNode = doc.nodeAt(sourcePos);
        if (!sourceNode) {
          return;
        }


        // Simple approach: use cut and paste
        const sourceEnd = sourcePos + dragData.nodeSize;

        // Create the move transaction
        if (sourcePos < dropPos) {
          // Moving down: insert after target, then delete source
          const insertPos = dropPos + node.nodeSize;
          tr.insert(insertPos, sourceNode);
          tr.delete(sourcePos, sourceEnd);
        } else {
          // Moving up: delete source first, then insert
          tr.delete(sourcePos, sourceEnd);
          tr.insert(dropPos, sourceNode);
        }

        // Apply the transaction
        editor.view.dispatch(tr);
      }

    } catch (error) {
    }
  };

  return (
    <NodeViewWrapper
      as="div"
      ref={nodeRef}
      className="group"
      data-node-view-wrapper
      onDragOver={handleDragOver}
      onDragLeave={handleDragLeave}
      onDrop={(event: React.DragEvent<Element>) => {
        handleDrop(event);
        setIsDragOver(false);
      }}
      css={{
        display: 'flex',
        gap: '0.5rem',
        width: '100%',
        position: 'relative',
        opacity: isDragging ? 0.5 : 1,
        transition: 'all 0.2s ease',
        borderTop: isDragOver ? '2px solid #3b82f6' : '2px solid transparent',
        borderBottom: isDragOver ? '2px solid #3b82f6' : '2px solid transparent',
        backgroundColor: isDragOver ? 'rgba(59, 130, 246, 0.05)' : 'transparent',
        borderRadius: isDragOver ? '4px' : '0px',
      }}
    >
      <section
        aria-label="left-menu"
        css={{ display: 'flex', marginTop: '0.5rem', gap: '0.375rem' }}
      >
        <div css={{ position: 'relative' }}>
          <button
            ref={buttonRef}
            type="button"
            css={{
              opacity: 0,
              transition: 'opacity 0.2s ease, background-color 0.2s ease',
              padding: '4px',
              borderRadius: '4px',
              border: '1px solid #E8EAED',
              backgroundColor: 'transparent',
              cursor: 'pointer',
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              borderStyle: "none",
              width: "32px",
              height: "32px",

              '&:hover': {
                backgroundColor: 'rgba(0, 0, 0, 0.1)',
              },

              '.group:hover &': {
                opacity: 1,
              },

              '&:focus': {
                outline: '2px solid #3b82f6',
                outlineOffset: '1px',
                opacity: 1,
              },
            }}
            onClick={() => setShowMenu(!showMenu)}
            aria-label="新しいブロックを追加"
            aria-expanded={showMenu}
            aria-haspopup="true"
          >
            <RiAddLine
              css={{
                color: 'rgba(75, 85, 99, 1)',
                transition: 'color 0.2s ease',

                '&:hover': {
                  color: 'rgba(55, 65, 81, 1)',
                },
              }}
            />
          </button>

          {showMenu && (
            <div
              ref={menuRef}
              css={{
                position: 'absolute',
                left: 0,
                marginTop: '0.25rem',
                width: '12rem',
                backgroundColor: 'white',
                borderRadius: '0.5rem',
                boxShadow: '0 10px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04)',
                border: '1px solid rgba(229, 231, 235, 1)',
                padding: '0.5rem',
                zIndex: 50,
                maxHeight: '20rem',
                overflowY: 'auto',
                animation: 'fadeIn 0.15s ease-out',

                '@keyframes fadeIn': {
                  from: {
                    opacity: 0,
                    transform: 'translateY(-4px)',
                  },
                  to: {
                    opacity: 1,
                    transform: 'translateY(0)',
                  },
                },
              }}
              role="menu"
            >
              {BLOCK_TYPES.map(({ type, label, icon: Icon }) => (
                <button
                  key={type}
                  type="button"
                  css={{
                    width: '100%',
                    display: 'flex',
                    alignItems: 'center',
                    gap: '0.75rem',
                    textAlign: 'left',
                    padding: '0.625rem 0.75rem',
                    fontSize: '0.875rem',
                    fontWeight: '400',
                    color: 'rgba(55, 65, 81, 1)',
                    backgroundColor: 'transparent',
                    border: 'none',
                    borderRadius: '0.375rem',
                    borderStyle: "none !important",
                    cursor: 'pointer',
                    transition: 'all 0.15s ease-in-out',
                    position: 'relative',

                    '&:hover': {
                      backgroundColor: 'rgba(243, 244, 246, 1)',
                      color: 'rgba(17, 24, 39, 1)',
                      transform: 'translateX(2px)',
                    },

                    '&:active': {
                      backgroundColor: 'rgba(229, 231, 235, 1)',
                      transform: 'translateX(1px)',
                    },

                    '&:focus': {
                      outline: '2px solid #3b82f6',
                      outlineOffset: '1px',
                      backgroundColor: 'rgba(243, 244, 246, 1)',
                    },
                  }}
                  onClick={() => type === 'pageLink' ? handlePageLinkClick() : createNodeAfter(type)}
                  role="menuitem"
                >
                  <Icon
                    css={{
                      color: 'rgba(107, 114, 128, 1)',
                      flexShrink: 0,
                      transition: 'color 0.15s ease-in-out',
                    }}
                  />
                  <span
                    css={{
                      flex: 1,
                      fontWeight: '500',
                    }}
                  >
                    {label}
                  </span>
                </button>
              ))}
            </div>
          )}
        </div>
        <div css={{ position: 'relative' }}>
          <div
            ref={dragButtonRef}
            css={{
              opacity: 0,
              transition: 'opacity 0.2s, background-color 0.2s',
              padding: '0.125rem',
              borderRadius: '0.25rem',
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              position: 'relative',
              '&:hover': {
                backgroundColor: 'rgba(0, 0, 0, 0.05)',
              },
              '.group:hover &': {
                opacity: 1,
              },
            }}
            contentEditable={false}
            aria-label="ブロックをドラッグまたはアクション"
            title="ドラッグしてブロックを移動、またはクリックでアクション"
          >
            {/* Single draggable element that contains the icon */}
            <div
              draggable={true}
              onDragStart={handleDragStart}
              onDragEnd={handleDragEnd}
              data-drag-handle="true"
              data-testid="drag-handle"
              css={{
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
                cursor: 'grab',
                padding: '2px',
                borderRadius: '2px',
                userSelect: 'none',
                border: '1px solid #E8EAED',
                '&:hover': {
                  backgroundColor: 'rgba(0, 0, 0, 0.1)',
                },
                '&:active': {
                  cursor: 'grabbing',
                },
                '&:focus': {
                  outline: '2px solid #3b82f6',
                  outlineOffset: '1px',
                },
              }}
              tabIndex={0}
              role="button"
              aria-label="ドラッグしてブロックを移動"
              onMouseDown={() => {
                // Don't prevent default here - let drag work naturally
              }}
              onMouseUp={() => {
              }}
              onClick={(e) => {
                // Only handle click if we're not in the middle of a drag
                e.stopPropagation();
                if (!isDragging) {
                  setShowDragMenu(!showDragMenu);
                }
              }}
            >
              <RiDraggable
                css={{
                  color: 'rgba(107, 114, 128, 1)',
                  transition: 'color 0.2s',
                  pointerEvents: 'none', // Let parent handle all events
                  '&:hover': {
                    color: 'rgba(75, 85, 99, 1)',
                  },
                }}
              />
            </div>
          </div>

          {showDragMenu && (
            <div
              ref={dragMenuRef}
              css={{
                position: 'absolute',
                left: 0,
                marginTop: '0.25rem',
                width: '8rem',
                backgroundColor: 'white',
                borderRadius: '0.5rem',
                boxShadow: '0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06)',
                border: '1px solid rgba(229, 231, 235, 1)',
                padding: '0.25rem',
                zIndex: 50,
              }}
              role="menu"
            >
              <button
                type="button"
                css={{
                  width: '100%',
                  display: 'flex',
                  alignItems: 'center',
                  gap: '0.5rem',
                  textAlign: 'left',
                  padding: '0.5rem 0.75rem',
                  fontSize: '0.875rem',
                  fontWeight: '400',
                  color: 'rgba(55, 65, 81, 1)',
                  backgroundColor: 'transparent',
                  border: 'none',
                  borderRadius: '0.375rem',
                  borderStyle: "none !important",
                  cursor: 'pointer',
                  transition: 'all 0.15s ease-in-out',
                  '&:hover': {
                    backgroundColor: 'rgba(243, 244, 246, 1)',
                    color: 'rgba(17, 24, 39, 1)',
                  },
                }}
                onClick={handleDuplicateBlock}
                role="menuitem"
              >
                <RiFileCopyLine
                  css={{
                    width: '1rem',
                    height: '1rem',
                    color: 'rgba(107, 114, 128, 1)',
                    flexShrink: 0,
                  }}
                />
                <span>複製</span>
              </button>

              <button
                type="button"
                css={{
                  width: '100%',
                  display: 'flex',
                  alignItems: 'center',
                  gap: '0.5rem',
                  textAlign: 'left',
                  padding: '0.5rem 0.75rem',
                  fontSize: '0.875rem',
                  fontWeight: '400',
                  color: 'rgba(220, 38, 38, 1)', // Red color for delete
                  backgroundColor: 'transparent',
                  border: 'none',
                  borderRadius: '0.375rem',
                  borderStyle: "none !important",
                  cursor: 'pointer',
                  transition: 'all 0.15s ease-in-out',
                  '&:hover': {
                    backgroundColor: 'rgba(254, 242, 242, 1)', // Light red background
                    color: 'rgba(185, 28, 28, 1)',
                  },
                }}
                onClick={handleDeleteBlock}
                role="menuitem"
              >
                <RiDeleteBinLine
                  css={{
                    width: '1rem',
                    height: '1rem',
                    color: 'rgba(220, 38, 38, 1)',
                    flexShrink: 0,
                  }}
                />
                <span>削除</span>
              </button>
            </div>
          )}
        </div>
      </section>
      <div style={{ position: 'relative', width: '100%' }}>
        <NodeViewContent
          css={{
            width: '100%',
          }}
        />
        {showPlaceholder && (
          <div
            style={{
              position: 'absolute',
              top: '8px',
              left: '12px',
              right: 0,
              pointerEvents: 'none',
              color: '#9ca3af',
              fontSize: '16px',
              fontWeight: 400,
              zIndex: 1,
              lineHeight: '1.5',
            }}
          >
            ページリンクを挿入
          </div>
        )}
      </div>

      <PageLinkDropdown
        opened={showPageLinkDropdown}
        onClose={() => {
          setShowPageLinkDropdown(false);
          setShowPlaceholder(false);
        }}
        onSelect={handlePageLinkSelect}
        portalId={portal_id}
        position={dropdownPosition}
      />
    </NodeViewWrapper>
  );
};
