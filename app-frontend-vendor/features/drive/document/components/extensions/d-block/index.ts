import { Node, mergeAttributes } from '@tiptap/core';
import { ReactNodeViewRenderer } from '@tiptap/react';
import { TextSelection } from 'prosemirror-state';
import { DBlockNodeView } from './d-block-node-view';

export interface DBlockOptions {
  HTMLAttributes: Record<string, string | number>;
}

declare module '@tiptap/core' {
  interface Commands<ReturnType> {
    dBlock: {
      /**
       * Toggle a dBlock
       */
      setDBlock: (position?: number) => ReturnType;
    };
  }
}

export const DBlock = Node.create<DBlockOptions>({
  name: 'dBlock',
  priority: 1000,
  group: 'dBlock',
  content: 'block+',
  draggable: true,
  defining: true,
  selectable: false,
  inline: false,

  addOptions() {
    return {
      HTMLAttributes: {},
    };
  },

  parseHTML() {
    return [{ tag: 'div[data-type="d-block"]' }];
  },

  renderHTML({ HTMLAttributes }) {
    return [
      'div',
      mergeAttributes(HTMLAttributes, { 'data-type': 'd-block' }),
      0,
    ];
  },

  addCommands() {
    return {
      setDBlock:
        (position) =>
        ({ state, chain }) => {
          const {
            selection: { from },
            doc,
          } = state;

          const pos = position !== undefined && position !== null ? position : from;
          
          // Create a new dBlock with a paragraph
          const dBlock = state.schema.nodes.dBlock.create(
            {},
            state.schema.nodes.paragraph.create()
          );
          
          // Insert the dBlock at the specified position
          const tr = state.tr.insert(pos, dBlock);
          
          // Set the selection to the new paragraph
          const resolvedPos = doc.resolve(pos + 1); // +1 to move inside the new block
          const selection = TextSelection.near(resolvedPos);
          tr.setSelection(selection);
          
          // Apply the transaction
          return chain().setMeta('addToHistory', true).command(({ tr, dispatch }) => {
            if (dispatch) {
              dispatch(tr);
              return true;
            }
            return false;
          }).run();
        },
    };
  },

  addNodeView() {
    return ReactNodeViewRenderer(DBlockNodeView);
  },

  // Add any necessary ProseMirror plugins
  addProseMirrorPlugins() {
    return [];
  },

  addKeyboardShortcuts() {
    return {
      'Mod-Alt-0': () => this.editor.commands.setDBlock(),
      Enter: ({ editor }) => {
        const { state } = editor;
        const { selection } = state;
        const { $head, from, to } = selection;
        const { depth } = $head;

        // Get current node and its parent
        const currentNode = $head.node(depth);
        const parentNode = depth > 0 ? $head.node(depth - 1) : null;

        // Detect list context more accurately
        let isInList = false;

        // Check all parent nodes for list context
        for (let d = depth; d >= 0; d--) {
          const nodeAtDepth = $head.node(d);
          if (nodeAtDepth.type.name === 'listItem' ||
              nodeAtDepth.type.name === 'bulletList' ||
              nodeAtDepth.type.name === 'orderedList') {
            isInList = true;
            break;
          }
        }

        // If we're in a list, let Tiptap's list extensions handle it naturally
        // This provides better nested list behavior and focus management
        if (isInList) {
          // Check if we're in an empty list item at the start
          const isEmptyListItem = currentNode.type.name === 'paragraph' &&
                                  currentNode.content.size === 0 &&
                                  $head.parentOffset === 0;

          if (isEmptyListItem) {
            // Try to lift out of the list using Tiptap's built-in command
            if (editor.can().liftListItem('listItem')) {
              return editor.chain().liftListItem('listItem').run();
            } else {
              // If we can't lift, we're at the top level - exit the list
              // Let Tiptap handle this naturally by returning false
              return false;
            }
          }

          // For all other list cases, let Tiptap handle naturally
          return false;
        }

        // Handle dBlock specific behavior only when not in lists
        if (parentNode?.type.name === 'dBlock') {
          const isAtEnd = $head.parentOffset === $head.parent.content.size;
          const isEmpty = $head.parent.content.size === 0;
          const isEmptyParagraph = currentNode.type.name === 'paragraph' &&
                                  currentNode.content.size === 0;

          // If we're at the end of a paragraph or it's empty, create new dBlock
          if ((isAtEnd || isEmpty || isEmptyParagraph) && from === to) {
            const dBlockDepth = depth - 1;
            const dBlockEnd = $head.after(dBlockDepth);

            // Ensure we don't exceed document bounds
            if (dBlockEnd <= state.doc.content.size) {
              // Use Tiptap's insertContentAt for better transaction handling
              return editor.chain()
                .insertContentAt(dBlockEnd, {
                  type: 'dBlock',
                  content: [{ type: 'paragraph' }]
                })
                .setTextSelection(dBlockEnd + 1) // More precise selection
                .run();
            }
          } else {
            // If we're in the middle of text, use Tiptap's splitBlock
            return editor.chain().splitBlock().run();
          }
        }

        // Let Tiptap handle all other cases naturally
        return false;
      },

      // Add Tab handling for list indentation
      Tab: ({ editor }) => {
        const { state } = editor;
        const { selection } = state;
        const { $head } = selection;

        // Check if we're in a list item
        if ($head.node($head.depth)?.type.name === 'listItem' ||
            $head.node($head.depth - 1)?.type.name === 'listItem') {
          return editor.chain().sinkListItem('listItem').focus().run();
        }

        return false;
      },

      // Add Shift+Tab handling for list outdentation
      'Shift-Tab': ({ editor }) => {
        const { state } = editor;
        const { selection } = state;
        const { $head } = selection;

        // Check if we're in a list item
        if ($head.node($head.depth)?.type.name === 'listItem' ||
            $head.node($head.depth - 1)?.type.name === 'listItem') {
          return editor.chain().liftListItem('listItem').focus().run();
        }

        return false;
      },
    };
  },
});
