import React, { useState, useRef, useEffect } from 'react';
import { NodeViewWrapper, type <PERSON>deViewProps } from '@tiptap/react';
import { 
  RiImageEditLine, 
  RiDeleteBinLine, 
  RiDownloadLine,
  RiEditLine,
  RiCloseLine,
  RiCheckLine
} from "@remixicon/react";

interface CustomImageNodeViewProps extends NodeViewProps {
  // Additional props can be added here
}

export const CustomImageNodeView: React.FC<CustomImageNodeViewProps> = ({
  node,
  updateAttributes,
  deleteNode,
  getPos,
  editor,
}) => {
  const [showPopup, setShowPopup] = useState(false);
  const [isEditingCaption, setIsEditingCaption] = useState(false);
  const [captionValue, setCaptionValue] = useState(node.attrs.caption || '');
  const [isUploading, setIsUploading] = useState(false);
  
  const imageRef = useRef<HTMLImageElement>(null);
  const popupRef = useRef<HTMLDivElement>(null);
  const captionInputRef = useRef<HTMLInputElement>(null);
  const fileInputRef = useRef<HTMLInputElement>(null);

  const { src, alt, caption, width, height } = node.attrs;

  // Close popup when clicking outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (popupRef.current && !popupRef.current.contains(event.target as Node)) {
        setShowPopup(false);
      }
    };

    if (showPopup) {
      document.addEventListener('mousedown', handleClickOutside);
      return () => document.removeEventListener('mousedown', handleClickOutside);
    }
  }, [showPopup]);

  // Focus caption input when editing starts
  useEffect(() => {
    if (isEditingCaption && captionInputRef.current) {
      captionInputRef.current.focus();
      captionInputRef.current.select();
    }
  }, [isEditingCaption]);

  // Calculate responsive image dimensions (16:9 ratio)
  const calculateImageDimensions = (containerWidth: number = 600) => {
    const maxWidth = Math.min(containerWidth * 0.8, 600); // 80% of container, max 600px
    const aspectRatio = 16 / 9;
    const calculatedHeight = maxWidth / aspectRatio;
    
    return {
      width: maxWidth,
      height: calculatedHeight,
    };
  };

  const handleImageDoubleClick = () => {
    setShowPopup(true);
  };

  const handleReplaceImage = () => {
    fileInputRef.current?.click();
  };

  const handleFileChange = async (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (!file) return;

    // Validate file type
    if (!file.type.startsWith('image/')) {
      alert('Please select a valid image file.');
      return;
    }

    // Validate file size (max 5MB)
    const maxSize = 5 * 1024 * 1024; // 5MB
    if (file.size > maxSize) {
      alert('Image size should be less than 5MB.');
      return;
    }

    setIsUploading(true);
    
    try {
      // Convert to base64 for now (in production, upload to server)
      const reader = new FileReader();
      reader.onload = (e) => {
        const newSrc = e.target?.result as string;
        updateAttributes({
          src: newSrc,
          alt: file.name,
        });
        setShowPopup(false);
        setIsUploading(false);
      };
      reader.readAsDataURL(file);
    } catch (error) {
      console.error('Error uploading image:', error);
      alert('Failed to upload image. Please try again.');
      setIsUploading(false);
    }
  };

  const handleDeleteImage = () => {
    if (confirm('Are you sure you want to delete this image?')) {
      deleteNode();
    }
  };

  const handleSaveCaption = () => {
    updateAttributes({ caption: captionValue });
    setIsEditingCaption(false);
  };

  const handleCancelCaption = () => {
    setCaptionValue(node.attrs.caption || '');
    setIsEditingCaption(false);
  };

  const dimensions = calculateImageDimensions();

  return (
    <NodeViewWrapper
      as="figure"
      css={{
        position: 'relative',
        margin: '16px 0',
        textAlign: 'center',
        maxWidth: '100%',
        
        '&:hover .image-overlay': {
          opacity: 1,
        },
      }}
    >
      {/* Hidden file input */}
      <input
        ref={fileInputRef}
        type="file"
        accept="image/*"
        onChange={handleFileChange}
        css={{ display: 'none' }}
      />

      {/* Image container */}
      <div
        css={{
          position: 'relative',
          display: 'inline-block',
          maxWidth: '100%',
          borderRadius: '8px',
          overflow: 'hidden',
          boxShadow: '0 2px 8px rgba(0, 0, 0, 0.1)',
        }}
      >
        <img
          ref={imageRef}
          src={src}
          alt={alt}
          onDoubleClick={handleImageDoubleClick}
          css={{
            width: width || dimensions.width,
            height: height || dimensions.height,
            objectFit: 'cover',
            display: 'block',
            cursor: 'pointer',
            transition: 'transform 0.2s ease',
            
            '&:hover': {
              transform: 'scale(1.02)',
            },
          }}
        />

        {/* Loading overlay */}
        {isUploading && (
          <div
            css={{
              position: 'absolute',
              top: 0,
              left: 0,
              right: 0,
              bottom: 0,
              backgroundColor: 'rgba(0, 0, 0, 0.5)',
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              color: 'white',
              fontSize: '14px',
            }}
          >
            Uploading...
          </div>
        )}

        {/* Image overlay hint */}
        <div
          className="image-overlay"
          css={{
            position: 'absolute',
            top: '8px',
            right: '8px',
            backgroundColor: 'rgba(0, 0, 0, 0.7)',
            color: 'white',
            padding: '4px 8px',
            borderRadius: '4px',
            fontSize: '12px',
            opacity: 0,
            transition: 'opacity 0.2s ease',
            pointerEvents: 'none',
          }}
        >
          Double-click to edit
        </div>
      </div>

      {/* Caption */}
      <figcaption
        css={{
          marginTop: '8px',
          fontSize: '14px',
          color: '#666',
          fontStyle: 'italic',
          maxWidth: dimensions.width,
          margin: '8px auto 0',
        }}
      >
        {isEditingCaption ? (
          <div css={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
            <input
              ref={captionInputRef}
              type="text"
              value={captionValue}
              onChange={(e) => setCaptionValue(e.target.value)}
              onKeyDown={(e) => {
                if (e.key === 'Enter') {
                  handleSaveCaption();
                } else if (e.key === 'Escape') {
                  handleCancelCaption();
                }
              }}
              placeholder="Enter image caption..."
              css={{
                flex: 1,
                padding: '4px 8px',
                border: '1px solid #ddd',
                borderRadius: '4px',
                fontSize: '14px',
              }}
            />
            <button
              type="button"
              onClick={handleSaveCaption}
              css={{
                padding: '4px',
                border: 'none',
                backgroundColor: '#22c55e',
                color: 'white',
                borderRadius: '4px',
                cursor: 'pointer',
                display: 'flex',
                alignItems: 'center',

              }}
            >
              <RiCheckLine size={16} />
            </button>
            <button
              type="button"
              onClick={handleCancelCaption}
              css={{
                padding: '4px',
                border: 'none',
                backgroundColor: '#ef4444',
                color: 'white',
                borderRadius: '4px',
                cursor: 'pointer',
                display: 'flex',
                alignItems: 'center',
              }}
            >
              <RiCloseLine size={16} />
            </button>
          </div>
        ) : (
          <div
            onClick={() => setIsEditingCaption(true)}
            css={{
              cursor: 'pointer',
              padding: '4px',
              borderRadius: '4px',
              minHeight: '20px',
              
              '&:hover': {
                backgroundColor: 'rgba(0, 0, 0, 0.05)',
              },
            }}
          >
            {caption || 'Click to add caption...'}
          </div>
        )}
      </figcaption>

      {/* Management Popup */}
      {showPopup && (
        <div
          ref={popupRef}
          css={{
            position: 'absolute',
            top: '8px',
            right: '8px',
            backgroundColor: 'white',
            borderRadius: '8px',
            boxShadow: '0 4px 12px rgba(0, 0, 0, 0.15)',
            border: '1px solid rgba(0, 0, 0, 0.1)',
            padding: '8px',
            zIndex: 1000,
            minWidth: '160px',
          }}
        >
          <div css={{ display: 'flex', flexDirection: 'column', gap: '4px' }}>
            <button
              type="button"
              onClick={handleReplaceImage}
              disabled={isUploading}
              css={{
                display: 'flex',
                alignItems: 'center',
                gap: '8px',
                padding: '8px 12px',
                border: 'none',
                backgroundColor: 'transparent',
                borderRadius: '4px',
                cursor: 'pointer',
                fontSize: '14px',
                width: '100%',
                textAlign: 'left',
                borderStyle: "none !important",
                
                '&:hover': {
                  backgroundColor: 'rgba(0, 0, 0, 0.05)',
                },
                
                '&:disabled': {
                  opacity: 0.5,
                  cursor: 'not-allowed',
                },
              }}
            >
              <RiImageEditLine size={16} />
              Replace Image
            </button>
            
            <button
              type="button"
              onClick={() => setIsEditingCaption(true)}
              css={{
                display: 'flex',
                alignItems: 'center',
                gap: '8px',
                padding: '8px 12px',
                border: 'none',
                backgroundColor: 'transparent',
                borderRadius: '4px',
                cursor: 'pointer',
                fontSize: '14px',
                width: '100%',
                textAlign: 'left',
                borderStyle: "none !important",
                
                '&:hover': {
                  backgroundColor: 'rgba(0, 0, 0, 0.05)',
                },
              }}
            >
              <RiEditLine size={16} />
              Edit Caption
            </button>
            
            <button
              type="button"
              onClick={handleDeleteImage}
              css={{
                display: 'flex',
                alignItems: 'center',
                gap: '8px',
                padding: '8px 12px',
                border: 'none',
                backgroundColor: 'transparent',
                borderRadius: '4px',
                cursor: 'pointer',
                fontSize: '14px',
                width: '100%',
                textAlign: 'left',
                borderStyle: "none !important",
                color: '#ef4444',
                
                '&:hover': {
                  backgroundColor: 'rgba(239, 68, 68, 0.1)',
                },
              }}
            >
              <RiDeleteBinLine size={16} />
              Delete Image
            </button>
          </div>
        </div>
      )}
    </NodeViewWrapper>
  );
};
