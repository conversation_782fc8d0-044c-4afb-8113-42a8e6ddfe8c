import { Node, mergeAttributes } from '@tiptap/core';
import { ReactNodeViewRenderer } from '@tiptap/react';
import { PageLinkNodeView } from './page-link-node-view';

export interface PageLinkOptions {
  HTMLAttributes: Record<string, string | number>;
}

export interface PageLinkAttributes {
  fileId: number;
  fileName: string;
  url: string;
}

declare module '@tiptap/core' {
  interface Commands<ReturnType> {
    pageLink: {
      /**
       * Insert a page link
       */
      setPageLink: (attributes: PageLinkAttributes) => ReturnType;
    };
  }
}

export const PageLink = Node.create<PageLinkOptions>({
  name: 'pageLink',
  priority: 1000,
  group: 'block',
  content: '',
  draggable: true,
  selectable: true,
  inline: false,
  atom: true,

  addOptions() {
    return {
      HTMLAttributes: {},
    };
  },

  addAttributes() {
    return {
      fileId: {
        default: null,
        parseHTML: element => element.getAttribute('data-file-id'),
        renderHTML: attributes => {
          if (!attributes.fileId) {
            return {};
          }
          return {
            'data-file-id': attributes.fileId,
          };
        },
      },
      fileName: {
        default: '',
        parseHTML: element => element.getAttribute('data-file-name'),
        renderHTML: attributes => {
          if (!attributes.fileName) {
            return {};
          }
          return {
            'data-file-name': attributes.fileName,
          };
        },
      },
      url: {
        default: '',
        parseHTML: element => element.getAttribute('data-url'),
        renderHTML: attributes => {
          if (!attributes.url) {
            return {};
          }
          return {
            'data-url': attributes.url,
          };
        },
      },
    };
  },

  parseHTML() {
    return [{ tag: 'div[data-type="page-link"]' }];
  },

  renderHTML({ HTMLAttributes }) {
    return [
      'div',
      mergeAttributes(HTMLAttributes, { 'data-type': 'page-link' }),
      0,
    ];
  },

  addCommands() {
    return {
      setPageLink:
        (attributes) =>
        ({ commands }) => {
          return commands.insertContent({
            type: this.name,
            attrs: attributes,
          });
        },
    };
  },

  addNodeView() {
    return ReactNodeViewRenderer(PageLinkNodeView);
  },
});
