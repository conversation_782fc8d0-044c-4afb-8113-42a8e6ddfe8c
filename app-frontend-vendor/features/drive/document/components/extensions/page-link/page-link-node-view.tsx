import React, { useState, useRef, useEffect } from 'react';
import { NodeViewWrapper, type NodeViewProps } from '@tiptap/react';
import { RiFileLine, RiEditLine, RiFileCopyLine, RiDeleteBinLine } from '@remixicon/react';

interface ActionMenuProps {
  isOpen: boolean;
  position: { top: number; left: number };
  onClose: () => void;
  onEdit: () => void;
  onDuplicate: () => void;
  onDelete: () => void;
}

const ActionMenu: React.FC<ActionMenuProps> = ({
  isOpen,
  position,
  onClose,
  onEdit,
  onDuplicate,
  onDelete,
}) => {
  const menuRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (menuRef.current && !menuRef.current.contains(event.target as Node)) {
        onClose();
      }
    };

    if (isOpen) {
      document.addEventListener('mousedown', handleClickOutside);
    }

    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, [isOpen, onClose]);

  if (!isOpen) return null;

  return (
    <div
      ref={menuRef}
      style={{
        position: 'fixed',
        top: position.top,
        left: position.left,
        width: '200px',
        backgroundColor: 'white',
        borderRadius: '8px',
        boxShadow: '0 8px 24px rgba(0, 0, 0, 0.12), 0 4px 8px rgba(0, 0, 0, 0.08)',
        border: '1px solid #e1e5e9',
        zIndex: 1000,
        overflow: 'hidden',
      }}
    >
      <div style={{ padding: '8px 0' }}>
        <div
          onClick={onEdit}
          style={{
            padding: '12px 16px',
            cursor: 'pointer',
            display: 'flex',
            alignItems: 'center',
            gap: '12px',
            fontSize: '14px',
            color: '#374151',
            transition: 'background-color 0.15s ease',
            borderBottom: '1px solid #f1f3f4',
          }}
          onMouseEnter={(e) => {
            e.currentTarget.style.backgroundColor = '#f8f9fa';
          }}
          onMouseLeave={(e) => {
            e.currentTarget.style.backgroundColor = 'transparent';
          }}
        >
          <RiEditLine size={16} color="#6b7280" />
          ページリンクを変更
        </div>

        <div
          onClick={onDuplicate}
          style={{
            padding: '12px 16px',
            cursor: 'pointer',
            display: 'flex',
            alignItems: 'center',
            gap: '12px',
            fontSize: '14px',
            color: '#374151',
            transition: 'background-color 0.15s ease',
            borderBottom: '1px solid #f1f3f4',
          }}
          onMouseEnter={(e) => {
            e.currentTarget.style.backgroundColor = '#f8f9fa';
          }}
          onMouseLeave={(e) => {
            e.currentTarget.style.backgroundColor = 'transparent';
          }}
        >
          <RiFileCopyLine size={16} color="#6b7280" />
          複製
        </div>

        <div
          onClick={onDelete}
          style={{
            padding: '12px 16px',
            cursor: 'pointer',
            display: 'flex',
            alignItems: 'center',
            gap: '12px',
            fontSize: '14px',
            color: '#dc2626',
            transition: 'background-color 0.15s ease',
          }}
          onMouseEnter={(e) => {
            e.currentTarget.style.backgroundColor = '#fef2f2';
          }}
          onMouseLeave={(e) => {
            e.currentTarget.style.backgroundColor = 'transparent';
          }}
        >
          <RiDeleteBinLine size={16} color="#dc2626" />
          削除
        </div>
      </div>
    </div>
  );
};

export const PageLinkNodeView: React.FC<NodeViewProps> = ({
  node,
  deleteNode,
  selected,
  editor,
  getPos,
}) => {
  const { fileName, url, fileId } = node.attrs;
  const [showActionMenu, setShowActionMenu] = useState(false);
  const [menuPosition, setMenuPosition] = useState({ top: 0, left: 0 });
  const [clickTimeout, setClickTimeout] = useState<NodeJS.Timeout | null>(null);
  const nodeRef = useRef<HTMLDivElement>(null);

  const handleClick = (e: React.MouseEvent) => {
    e.preventDefault();

    // Clear any existing timeout
    if (clickTimeout) {
      clearTimeout(clickTimeout);
      setClickTimeout(null);
    }

    // Set a timeout to handle single click
    const timeout = setTimeout(() => {
      if (url) {
        window.open(url, '_blank');
      }
      setClickTimeout(null);
    }, 300); // 300ms delay to detect double click

    setClickTimeout(timeout);
  };

  const handleDoubleClick = (e: React.MouseEvent) => {
    e.preventDefault();
    e.stopPropagation();

    // Clear the single click timeout to prevent opening new tab
    if (clickTimeout) {
      clearTimeout(clickTimeout);
      setClickTimeout(null);
    }

    if (nodeRef.current) {
      const rect = nodeRef.current.getBoundingClientRect();
      setMenuPosition({
        top: rect.bottom + 8,
        left: rect.left,
      });
      setShowActionMenu(true);
    }
  };



  const handleEdit = () => {
    setShowActionMenu(false);

    // Delete current page link
    deleteNode();

    // Dispatch custom event to trigger d-block dropdown
    const event = new CustomEvent('openPageLinkDropdown', {
      detail: {
        position: menuPosition, // Use the same position as action menu
      }
    });
    window.dispatchEvent(event);
  };

  const handleDuplicate = () => {
    setShowActionMenu(false);
    if (editor && getPos) {
      const pos = getPos();
      editor.chain().focus().insertContentAt(pos + 1, {
        type: 'pageLink',
        attrs: { fileId, fileName, url },
      }).run();
    }
  };

  const handleMenuDelete = () => {
    setShowActionMenu(false);
    deleteNode();
  };

  // Cleanup timeout on unmount
  useEffect(() => {
    return () => {
      if (clickTimeout) {
        clearTimeout(clickTimeout);
      }
    };
  }, [clickTimeout]);

  return (
    <>
      <NodeViewWrapper
        as="div"
        ref={nodeRef}
        css={{
          margin: '0.75rem 0',
          padding: '12px 16px',
          border: '1px solid #e5e7eb',
          borderRadius: '6px',
          backgroundColor: selected ? '#e0f2fe' : '#ffffff',
          cursor: 'pointer',
          transition: 'all 0.2s ease',
          position: 'relative',
          '&:hover': {
            backgroundColor: selected ? '#b3e5fc' : '#f9fafb',
            borderColor: selected ? '#81d4fa' : '#d1d5db',
            boxShadow: '0 1px 3px rgba(0, 0, 0, 0.1)',
          },
        }}
        onClick={handleClick}
        onDoubleClick={handleDoubleClick}
      >
      <div
        css={{
          display: 'flex',
          alignItems: 'center',
          gap: '12px',
        }}
      >
        <div
          css={{
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            width: '20px',
            height: '20px',
            flexShrink: 0,
          }}
        >
          <RiFileLine
            css={{
              width: '16px',
              height: '16px',
              color: '#6b7280',
            }}
          />
        </div>

        <div css={{ flex: 1, minWidth: 0 }}>
          <div
            css={{
              fontSize: '14px',
              fontWeight: '400',
              color: '#374151',
              overflow: 'hidden',
              textOverflow: 'ellipsis',
              whiteSpace: 'nowrap',
              lineHeight: '1.4',
            }}
          >
            {fileName || 'Untitled Document'}
          </div>
        </div>
      </div>
      </NodeViewWrapper>

      <ActionMenu
        isOpen={showActionMenu}
        position={menuPosition}
        onClose={() => setShowActionMenu(false)}
        onEdit={handleEdit}
        onDuplicate={handleDuplicate}
        onDelete={handleMenuDelete}
      />
    </>
  );
};
