import React from 'react';
import { NodeViewWrapper, type NodeViewProps } from '@tiptap/react';
import { RiFileLine, RiExternalLinkLine } from '@remixicon/react';

export const PageLinkNodeView: React.FC<NodeViewProps> = ({
  node,
  deleteNode,
  selected,
}) => {
  const { fileName, url } = node.attrs;

  const handleClick = () => {
    if (url) {
      window.open(url, '_blank');
    }
  };

  const handleDelete = (e: React.MouseEvent) => {
    e.stopPropagation();
    deleteNode();
  };

  return (
    <NodeViewWrapper
      as="div"
      css={{
        margin: '0.75rem 0',
        padding: '12px 16px',
        border: '1px solid #e5e7eb',
        borderRadius: '6px',
        backgroundColor: selected ? '#e0f2fe' : '#ffffff',
        cursor: 'pointer',
        transition: 'all 0.2s ease',
        position: 'relative',
        '&:hover': {
          backgroundColor: selected ? '#b3e5fc' : '#f9fafb',
          borderColor: selected ? '#81d4fa' : '#d1d5db',
          boxShadow: '0 1px 3px rgba(0, 0, 0, 0.1)',
        },
      }}
      onClick={handleClick}
    >
      <div
        css={{
          display: 'flex',
          alignItems: 'center',
          gap: '12px',
        }}
      >
        <div
          css={{
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            width: '20px',
            height: '20px',
            flexShrink: 0,
          }}
        >
          <RiFileLine
            css={{
              width: '16px',
              height: '16px',
              color: '#6b7280',
            }}
          />
        </div>

        <div css={{ flex: 1, minWidth: 0 }}>
          <div
            css={{
              fontSize: '14px',
              fontWeight: '400',
              color: '#374151',
              overflow: 'hidden',
              textOverflow: 'ellipsis',
              whiteSpace: 'nowrap',
              lineHeight: '1.4',
            }}
          >
            {fileName || 'Untitled Document'}
          </div>
        </div>

        <div
          css={{
            display: 'flex',
            alignItems: 'center',
            gap: '8px',
            opacity: 0.7,
            '&:hover': {
              opacity: 1,
            },
          }}
        >
          <RiExternalLinkLine
            css={{
              width: '14px',
              height: '14px',
              color: '#6b7280',
            }}
          />
          <button
            type="button"
            onClick={handleDelete}
            css={{
              width: '20px',
              height: '20px',
              padding: '0',
              borderRadius: '4px',
              border: 'none !important',
              outline: 'none !important',
              boxShadow: 'none !important',
              backgroundColor: 'transparent',
              color: '#6b7280',
              cursor: 'pointer',
              fontSize: '14px',
              lineHeight: 1,
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              appearance: 'none',
              WebkitAppearance: 'none',
              MozAppearance: 'none',
              '&:hover': {
                backgroundColor: '#f3f4f6',
                color: '#374151',
                border: 'none !important',
                outline: 'none !important',
                boxShadow: 'none !important',
              },
              '&:focus': {
                outline: 'none !important',
                border: 'none !important',
                boxShadow: 'none !important',
              },
              '&:active': {
                outline: 'none !important',
                border: 'none !important',
                boxShadow: 'none !important',
              },
            }}
          >
            ×
          </button>
        </div>
      </div>
    </NodeViewWrapper>
  );
};
