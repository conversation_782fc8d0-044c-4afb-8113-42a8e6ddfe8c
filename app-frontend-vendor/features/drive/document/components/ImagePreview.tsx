import { Box, Button, FileInput, Text } from "@mantine/core";
import Image from "next/image";
import { useEffect, useRef, useState } from "react";
import { propcolors } from "styles/colors";
import { useThumbnailLoader } from "../hooks/useThumbnailLoader";

type ImagePreviewProps = {
  file?: File;
  imageUrl?: string;
  thumbnailPath?: string;
  itemId?: number;
  onFileChange?: (file: File | null) => void;
  onRemove?: () => void;
  onThumbnailRemove?: () => void;

  showChangeButton?: boolean;
  showRemoveButton?: boolean;
  enableEditMode?: boolean;
  onSaveThumbnail?: (file: File) => Promise<void>;
  onDeleteThumbnail?: () => Promise<void>;
};

export const ImagePreview: React.FC<ImagePreviewProps> = ({
  file,
  imageUrl: propImageUrl,
  thumbnailPath,
  itemId,
  onFileChange,
  onRemove,
  onThumbnailRemove,
  showChangeButton = true,
  showRemoveButton = true,
  enableEditMode = false,
  onSaveThumbnail,
  onDeleteThumbnail,
}) => {
  const [imageUrl, setImageUrl] = useState<string>("");
  const [imageError, setImageError] = useState<boolean>(false);
  const [hasNewFile, setHasNewFile] = useState<boolean>(false);
  const [showEditMode, setShowEditMode] = useState<boolean>(false);
  const [isUploading, setIsUploading] = useState<boolean>(false);
  const fileInputRef = useRef<HTMLButtonElement>(null);

  // Use thumbnail loader hook for API-based thumbnail loading
  const {
    thumbnailUrl: loadedThumbnailUrl,
    isLoading: isThumbnailLoading,
    error: thumbnailError,
    reloadThumbnail,
  } = useThumbnailLoader({
    itemId,
    thumbnailPath,
    enabled: !!itemId && !!thumbnailPath,
  });

  // Helper function to format thumbnail URL
  const formatThumbnailUrl = (path: string): string => {
    if (!path) return '';

    // If already absolute URL, return as is
    if (path.startsWith('http://') || path.startsWith('https://')) {
      return path;
    }

    // If starts with slash, return as is
    if (path.startsWith('/')) {
      return path;
    }

    // For relative paths, try different approaches
    // First try with leading slash
    const withSlash = `/${path}`;

    // You might need to adjust this based on your server setup
    // For example, if images are served from a specific endpoint:
    // return `/api/v1/drive/thumbnail/${path}`;
    // or if they're in a public directory:
    // return `/uploads/${path}`;

    return withSlash;
  };

  useEffect(() => {
    if (file) {
      const url = URL.createObjectURL(file);
      setImageUrl(url);
      setImageError(false);
      setHasNewFile(true);

      // Cleanup function to revoke object URL
      return () => {
        URL.revokeObjectURL(url);
      };
    } else if (propImageUrl) {
      setImageUrl(propImageUrl);
      setImageError(false);
      setHasNewFile(false);
    } else if (loadedThumbnailUrl && loadedThumbnailUrl !== "/icons/file.svg") {
      // Use loaded thumbnail URL from API
      console.log("Using loaded thumbnail URL:", loadedThumbnailUrl);
      setImageUrl(loadedThumbnailUrl);
      setImageError(false);
      setHasNewFile(false);
    } else if (thumbnailPath) {
      // Fallback to formatted thumbnail path
      const formattedUrl = formatThumbnailUrl(thumbnailPath);
      console.log("Original thumbnail path:", thumbnailPath);
      console.log("Formatted URL:", formattedUrl);
      setImageUrl(formattedUrl);
      setImageError(false);
      setHasNewFile(false);
    } else {
      setImageUrl("");
      setImageError(false);
      setHasNewFile(false);
    }
  }, [file, propImageUrl, thumbnailPath, loadedThumbnailUrl]);

  const handleImageClick = () => {
    fileInputRef.current?.click();
  };

  const handleThumbnailDelete = (e: React.MouseEvent) => {
    e.stopPropagation();
    if (hasNewFile || !thumbnailPath) {
      // If it's a new file or no thumbnail, just open file picker
      fileInputRef.current?.click();
    } else if (onThumbnailRemove) {
      // If it's an existing thumbnail, call the remove handler
      onThumbnailRemove();
    }
  };

  const handleFileInputChange = (newFile: File | null) => {
    if (newFile && onFileChange) {
      onFileChange(newFile);
    }
  };

  const handleRemoveImage = (e: React.MouseEvent) => {
    e.stopPropagation();
    if (hasNewFile && onRemove) {
      // Remove new file
      onRemove();
    } else if (thumbnailPath && onThumbnailRemove) {
      // Remove existing thumbnail
      onThumbnailRemove();
    }
  };

  const handleSaveImage = async () => {
    if (!file || !onSaveThumbnail) return;

    setIsUploading(true);
    try {
      await onSaveThumbnail(file);
      setShowEditMode(false);
      // Reload thumbnail after save
      if (reloadThumbnail) {
        await reloadThumbnail();
      }
    } catch (error) {
      console.error("Failed to save thumbnail:", error);
    } finally {
      setIsUploading(false);
    }
  };

  const handleDeleteImage = async () => {
    if (!onDeleteThumbnail) return;

    setIsUploading(true);
    try {
      await onDeleteThumbnail();
      setShowEditMode(false);
      // Reload thumbnail after delete
      if (reloadThumbnail) {
        await reloadThumbnail();
      }
    } catch (error) {
      console.error("Failed to delete thumbnail:", error);
    } finally {
      setIsUploading(false);
    }
  };

  return (
    <Box
      css={{
        display: "flex",
        flexDirection: "column",
        alignItems: "center",
        width: "100%",
        maxWidth: "100%",
      }}
    >
      <Box
        css={{
          position: "relative",
          width: "100%",
          marginBottom: "16px",
          minHeight: "200px",
          display: "flex",
          alignItems: "center",
          justifyContent: "center",
        }}
      >
        {imageError ? (
          <Box
            css={{
              width: "100%",
              height: "200px",
              display: "flex",
              flexDirection: "column",
              alignItems: "center",
              justifyContent: "center",
              backgroundColor: propcolors.gray[100],
              borderRadius: "4px",
              border: `1px solid ${propcolors.gray[300]}`,
              gap: "8px",
            }}
          >
            <Text
              size="sm"
              css={{
                color: propcolors.gray[500],
              }}
            >
              画像を読み込めませんでした
            </Text>
            {thumbnailPath && (
              <Text
                size="xs"
                css={{
                  color: propcolors.gray[400],
                  fontFamily: "monospace",
                }}
              >
                Path: {thumbnailPath}
              </Text>
            )}
            {thumbnailError && (
              <Text
                size="xs"
                css={{
                  color: propcolors.red[500],
                  marginTop: "4px",
                }}
              >
                Error: {thumbnailError}
              </Text>
            )}
          </Box>
        ) : imageUrl ? (
          <Box
            css={{
              position: "relative",
              width: "100%",
              height: "100%",
              minHeight: "200px",
              display: "flex",
              alignItems: "center",
              justifyContent: "center",
              backgroundColor: "#f7f8f9",
              borderRadius: "4px",
              cursor: onFileChange ? "pointer" : "default",
              "&:hover .edit-button": {
                opacity: 1,
              },
            }}
            onClick={onFileChange && !enableEditMode ? handleImageClick : undefined}
          >
            {/* Show loading state for thumbnail loading */}
            {isThumbnailLoading && itemId && (
              <Box
                css={{
                  position: "absolute",
                  top: "50%",
                  left: "50%",
                  transform: "translate(-50%, -50%)",
                  color: propcolors.gray[500],
                  fontSize: "14px",
                }}
              >
                読み込み中...
              </Box>
            )}

            {/* Use Next.js Image for loaded thumbnails, regular img for others */}
            {loadedThumbnailUrl && loadedThumbnailUrl !== "/icons/file.svg" && !file ? (
              <Image
                src={imageUrl}
                width={0}
                height={0}
                alt="サムネイル"
                sizes="100%"
                css={{
                  width: "100%",
                  height: "auto",
                  objectFit: "cover",
                  borderRadius: "4px",
                  display: "block",
                }}
                onError={() => {
                  console.error("Failed to load image:", imageUrl);
                  setImageError(true);
                }}
              />
            ) : (
              <img
                src={imageUrl}
                alt={file?.name || "サムネイル"}
                css={{
                  width: "100%",
                  height: "auto",
                  objectFit: "cover",
                  borderRadius: "4px",
                  display: "block",
                }}
                onError={() => {
                  console.error("Failed to load image:", imageUrl);
                  setImageError(true);
                }}
              />
            )}

            {/* Edit controls */}
            {enableEditMode && showEditMode && (
              <Box
                css={{
                  position: "absolute",
                  top: "10px",
                  right: "10px",
                  zIndex: 2,
                  display: "flex",
                  flexDirection: "column",
                  gap: "8px",
                }}
              >
                <Button
                  size="xs"
                  onClick={() => fileInputRef.current?.click()}
                  disabled={isUploading}
                  css={{
                    backgroundColor: "white",
                    color: propcolors.blue[600],
                    border: `1px solid ${propcolors.blue[300]}`,
                    boxShadow: "0 2px 4px rgba(0,0,0,0.1)",
                    "&:hover": {
                      backgroundColor: propcolors.backgroundHover,
                    },
                  }}
                >
                  変更
                </Button>

                {file && onSaveThumbnail && (
                  <Button
                    size="xs"
                    onClick={handleSaveImage}
                    loading={isUploading}
                    css={{
                      backgroundColor: propcolors.green[500],
                      color: "white",
                      "&:hover": {
                        backgroundColor: propcolors.green[600],
                      },
                    }}
                  >
                    保存
                  </Button>
                )}

                {thumbnailPath && onDeleteThumbnail && (
                  <Button
                    size="xs"
                    color="red"
                    onClick={handleDeleteImage}
                    disabled={isUploading}
                    css={{
                      backgroundColor: "white",
                      color: propcolors.red[600],
                      border: `1px solid ${propcolors.red[300]}`,
                      boxShadow: "0 2px 4px rgba(0,0,0,0.1)",
                      "&:hover": {
                        backgroundColor: propcolors.backgroundHover,
                      },
                    }}
                  >
                    削除
                  </Button>
                )}

                <Button
                  size="xs"
                  variant="outline"
                  onClick={() => {
                    setShowEditMode(false);
                    if (onRemove) onRemove();
                  }}
                  disabled={isUploading}
                >
                  キャンセル
                </Button>
              </Box>
            )}

            {/* Standard controls for non-edit mode */}
            {!enableEditMode && showRemoveButton && (
              <Box
                css={{
                  position: "absolute",
                  top: "10px",
                  right: "10px",
                  zIndex: 2,
                }}
              >
                <Button
                  variant="filled"
                  size="xs"
                  onClick={handleRemoveImage}
                  css={{
                    backgroundColor: "white",
                    color: propcolors.red[600],
                    border: `1px solid ${propcolors.red[300]}`,
                    boxShadow: "0 2px 4px rgba(0,0,0,0.1)",
                    fontSize: "12px",
                    padding: "4px 8px",
                    "&:hover": {
                      backgroundColor: propcolors.backgroundHover,
                    },
                  }}
                >
                  サムネイルの削除
                </Button>
              </Box>
            )}

            {/* Edit button (hover to show) */}
            {enableEditMode && !showEditMode && (
              <Button
                size="sm"
                className="edit-button"
                onClick={() => setShowEditMode(true)}
                css={{
                  position: "absolute",
                  top: "16px",
                  right: "16px",
                  opacity: 0,
                  transition: "opacity 0.2s",
                  backgroundColor: "rgba(0, 0, 0, 0.7)",
                  color: "white",
                  "&:hover": {
                    backgroundColor: "rgba(0, 0, 0, 0.8)",
                  },
                  "&:parent:hover": {
                    opacity: 1,
                  },
                }}
              >
                編集
              </Button>
            )}
          </Box>
        ) : (
          // Show file input when no image
          <Box
            css={{
              width: "100%",
              height: "200px",
              display: "flex",
              alignItems: "center",
              justifyContent: "center",
              backgroundColor: propcolors.inputBackground,
              borderRadius: "4px",
              border: `2px dashed ${propcolors.gray[300]}`,
              cursor: "pointer",
            }}
            onClick={handleImageClick}
          >
            <Text
              size="sm"
              css={{
                color: propcolors.gray[500],
              }}
            >
              サムネイルを選択してください
            </Text>
          </Box>
        )}
      </Box>

      {/* Hidden file input for changing image */}
      {onFileChange && (
        <FileInput
          ref={fileInputRef}
          style={{ display: "none" }}
          accept="image/*"
          onChange={handleFileInputChange}
        />
      )}

      {/* Additional info for existing thumbnails */}
      {thumbnailPath && !hasNewFile && (
        <Box css={{ marginTop: "8px" }}>
          <Text
            size="sm"
            css={{
              color: propcolors.gray[600],
              textAlign: "center",
            }}
          >
            既存のサムネイル
          </Text>
        </Box>
      )}
    </Box>
  );
};
