import React, { useState, useEffect, useRef } from 'react';
import { TextInput, Box, Text, ScrollArea } from '@mantine/core';
import { RiFileLine } from '@remixicon/react';
import { ax } from '../../../../utils/axios';
import useSWR from 'swr';

interface PageLinkDropdownProps {
  opened: boolean;
  onClose: () => void;
  onSelect: (fileId: number, fileName: string, url: string) => void;
  portalId: string | string[] | undefined;
  position: { top: number; left: number };
}

interface DriveFile {
  id: number;
  name: string;
  type: string;
  url?: string;
  content?: string;
}

interface DriveFileList {
  files: DriveFile[];
}

export const PageLinkDropdown: React.FC<PageLinkDropdownProps> = ({
  opened,
  onClose,
  onSelect,
  portalId,
  position,
}) => {
  const [searchQuery, setSearchQuery] = useState('');
  const [documents, setDocuments] = useState<DriveFile[]>([]);
  const dropdownRef = useRef<HTMLDivElement>(null);

  const fetcher = (url: string) =>
    ax
      .get(url)
      .then((res) => res.data)
      .catch((_err) => ({ files: [] }));

  // Fetch files from the portal
  const { data: fileData } = useSWR<DriveFileList>(
    portalId ? `api/v1/drive/file/0/list/${portalId}` : null,
    fetcher,
    { revalidateOnFocus: false }
  );

  // Filter files based on search query and file type
  useEffect(() => {
    let docs: DriveFile[] = [];

    if (fileData?.files) {
      // Filter for documents and normal files, exclude directories
      docs = fileData.files.filter(
        (file) => file.type === 'normal' && file.name.toLowerCase().includes(searchQuery.toLowerCase())
      );
    } else {
      // Add some demo documents for testing
      docs = [
        { id: 1, name: '名称未設定', type: 'normal' },
        { id: 2, name: 'PartnerProp導入事例.pdf', type: 'normal' },
        { id: 3, name: '営業資料_2024年度版.docx', type: 'normal' },
        { id: 4, name: 'プロジェクト企画書.pptx', type: 'normal' },
        { id: 5, name: '顧客リスト_最新版.xlsx', type: 'normal' },
        { id: 6, name: '【チラシ】これ1枚でわかる「PartnerProp」.pdf', type: 'normal' },
      ].filter(file => file.name.toLowerCase().includes(searchQuery.toLowerCase()));
    }

    setDocuments(docs);
  }, [fileData, searchQuery]);

  const handleFileSelect = (file: DriveFile) => {
    // Generate URL for the file
    const fileUrl = file.url || `${window.location.origin}/drive/${file.id}?portal_id=${portalId}`;
    onSelect(file.id, file.name, fileUrl);
    onClose();
  };

  // Close dropdown when clicking outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target as Node)) {
        onClose();
      }
    };

    if (opened) {
      document.addEventListener('mousedown', handleClickOutside);
    }

    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, [opened, onClose]);

  if (!opened) return null;

  // Calculate dynamic height based on number of items
  const totalItems = documents.length;
  const searchSectionHeight = 80; // Search input + padding
  const itemHeight = 50; // Height per item
  const maxVisibleItems = 5;
  const noResultsHeight = 72; // Height for "not found" message

  const contentHeight = totalItems > 0
    ? Math.min(totalItems * itemHeight, maxVisibleItems * itemHeight)
    : noResultsHeight;

  const totalHeight = searchSectionHeight + contentHeight;

  return (
    <div
      ref={dropdownRef}
      style={{
        position: 'fixed',
        top: position.top,
        left: position.left,
        width: '320px',
        height: `${totalHeight}px`,
        backgroundColor: 'white',
        borderRadius: '8px',
        boxShadow: '0 8px 24px rgba(0, 0, 0, 0.12), 0 4px 8px rgba(0, 0, 0, 0.08)',
        border: '1px solid #e1e5e9',
        zIndex: 1000,
        overflow: 'hidden',
        display: 'flex',
        flexDirection: 'column',
      }}
    >
      {/* Search section */}
      <div style={{ padding: '20px 20px 16px 20px', flexShrink: 0 }}>
        <TextInput
          placeholder="URLを貼り付けまたは検索"
          value={searchQuery}
          onChange={(e) => setSearchQuery(e.target.value)}
          styles={{
            input: {
              border: '1px solid #e5e7eb',
              borderRadius: '6px',
              padding: '10px 12px',
              fontSize: '14px',
              color: '#374151',
              '&::placeholder': {
                color: '#9ca3af',
              },
              '&:focus': {
                borderColor: '#3b82f6',
                boxShadow: '0 0 0 1px #3b82f6',
              },
            },
          }}
        />
      </div>

      {/* Documents sections */}
      <div style={{ borderTop: '1px solid #f1f3f4', flex: 1, overflow: 'hidden' }}>
        {documents.length > 0 ? (
          <ScrollArea
            h={contentHeight}
            styles={{
              scrollbar: {
                '&[data-orientation="vertical"]': {
                  width: '6px',
                },
                '&[data-orientation="vertical"] .mantine-ScrollArea-thumb': {
                  backgroundColor: '#d1d5db',
                  borderRadius: '3px',
                },
              },
            }}
          >
            <div>
              {documents.map((file, index) => (
                <Box
                  key={file.id}
                  p="14px 20px"
                  style={{
                    cursor: 'pointer',
                    transition: 'all 0.15s ease',
                    backgroundColor: 'transparent',
                    borderBottom: index < documents.length - 1 ? '1px solid #f1f3f4' : 'none',
                  }}
                  css={{
                    '&:hover': {
                      backgroundColor: '#f8f9fa',
                    },
                  }}
                  onClick={() => handleFileSelect(file)}
                >
                  <Box style={{ display: 'flex', alignItems: 'center', gap: '12px' }}>
                    <Box
                      style={{
                        display: 'flex',
                        alignItems: 'center',
                        justifyContent: 'center',
                        width: '18px',
                        height: '18px',
                        flexShrink: 0,
                      }}
                    >
                      <RiFileLine size={18} color="#9ca3af" />
                    </Box>
                    <Box style={{ flex: 1, minWidth: 0 }}>
                      <Text
                        size="sm"
                        fw={400}
                        c="#374151"
                        style={{
                          overflow: 'hidden',
                          textOverflow: 'ellipsis',
                          whiteSpace: 'nowrap',
                          lineHeight: '1.4',
                          fontSize: '14px',
                        }}
                      >
                        {file.name}
                      </Text>
                    </Box>
                  </Box>
                </Box>
              ))}
            </div>
          </ScrollArea>
        ) : (
          <Box ta="center" py="lg" style={{ padding: '24px 20px' }}>
            <Text size="sm" c="#9ca3af" style={{ fontSize: '14px' }}>
              {searchQuery ? 'ファイルが見つかりません' : 'ファイルがありません'}
            </Text>
          </Box>
        )}
      </div>
    </div>
  );
};
