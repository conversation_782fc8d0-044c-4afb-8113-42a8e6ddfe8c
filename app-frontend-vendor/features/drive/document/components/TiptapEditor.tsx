import Bold from '@tiptap/extension-bold';
import BulletList from '@tiptap/extension-bullet-list';
import DropCursor from '@tiptap/extension-dropcursor';
import GapCursor from '@tiptap/extension-gapcursor';
import HardBreak from '@tiptap/extension-hard-break';
import Heading from '@tiptap/extension-heading';
import History from '@tiptap/extension-history';
import HorizontalRule from '@tiptap/extension-horizontal-rule';
import Italic from '@tiptap/extension-italic';
import Link from '@tiptap/extension-link';
import ListItem from '@tiptap/extension-list-item';
import OrderedList from '@tiptap/extension-ordered-list';
import Paragraph from '@tiptap/extension-paragraph';
import Strike from '@tiptap/extension-strike';
import Text from '@tiptap/extension-text';
import {
  BubbleMenu,
  type Editor,
  EditorContent,
  JSONContent,
  useEditor,
} from '@tiptap/react';
import { DBlock } from './extensions/d-block';
import { Document } from './extensions/doc';
import { TrailingNode } from './extensions/trailing-node';
import { debounce } from "../../../../constants/debounce";
import { Code } from "@tiptap/extension-code";
import Blockquote from '@tiptap/extension-blockquote';
import { CodeBlock } from "@tiptap/extension-code-block";
import { FileHandler } from "@tiptap/extension-file-handler";
import { CustomImage } from './extensions/custom-image';
import { PageLink } from './extensions/page-link';
import { useEffect, useRef } from 'react';

import React from 'react';
// import Underline from '@tiptap/extension-underline';
// import TextStyle from '@tiptap/extension-text-style';
// import Color from '@tiptap/extension-color';
// import Focus from '@tiptap/extension-focus';

interface BlockContent {
  type: string;
  text: string;
  styles: Record<string, unknown>;
}

interface BlockProps {
  textColor: string;
  backgroundColor: string;
  textAlignment: string;
  level?: number;
  language?: string;
  name?: string;
  url?: string;
  caption?: string;
  showPreview?: boolean;
  previewWidth?: number;
  documentFileId?: number | string;
}

interface Block {
  id: string;
  type: string;
  props: BlockProps;
  content: BlockContent[];
  children: Block[];
}

interface TiptapEditorProps {
  portal_id?: string | string[];
  content?: any; // JSON content instead of HTML string
  onChange?: (content: any) => void; // JSON content instead of HTML
  onJSONChange?: (blocks: JSONContent) => void;
  onEditorReady?: (editor: any) => void;
  placeholder?: string;
  disabled?: boolean;
}

// Removed manual block conversion helpers. Use Tiptap JSON directly.


// Cấu trúc content mặc định cho Tiptap Editor
const defaultContent = {
  type: 'doc',
  content: [
    {
      type: 'paragraph',
      content: []
    }
  ]
}; // Use standard Tiptap doc/paragraph as default, not dBlock




export const TiptapEditor: React.FC<TiptapEditorProps> = ({
  portal_id,
  content = defaultContent,
  onChange = () => {},
  onJSONChange = () => {},
  onEditorReady = () => {},
  placeholder: _placeholder = "内容を入力してください...",
  disabled = false,
}) => {
  // Track if user is currently typing to prevent content updates
  const isTypingRef = useRef(false);
  const typingTimeoutRef = useRef<NodeJS.Timeout | null>(null);
  const isFileHandlerActiveRef = useRef(false);
  const editor = useEditor({
    // create content default
    content: content || defaultContent,
    onUpdate: debounce(({ editor, transaction }) => {
      // Skip all programmatic updates to prevent focus issues
      if (transaction.getMeta('preventUpdate') ||
          transaction.getMeta('addToHistory') === false ||
          transaction.getMeta('userEvent') === 'programmatic' ||
          !transaction.docChanged) {
        return;
      }

      // Mark that user is typing
      isTypingRef.current = true;

      // Clear previous timeout
      if (typingTimeoutRef.current) {
        clearTimeout(typingTimeoutRef.current);
      }

      // Set timeout to mark typing as finished
      typingTimeoutRef.current = setTimeout(() => {
        isTypingRef.current = false;
      }, 1000); // Consider typing finished after 1 second of inactivity

      // Only process user-initiated changes
      const json = editor.getJSON();
      // Pass Tiptap's JSON directly to callbacks for clarity and consistency
      const currentSelection = editor.state.selection;
      const currentPos = currentSelection.anchor;
      const isFocused = editor.isFocused;

      setTimeout(() => {
        if (onChange) {
          onChange(json);
        }
        if (onJSONChange) {
          onJSONChange(json); // Pass JSON directly, not custom block format
        }

        // Restore focus and selection after callbacks if editor was focused
        if (isFocused && editor && !editor.isDestroyed) {
          setTimeout(() => {
            try {
              // Only restore if editor lost focus
              if (!editor.isFocused) {
                const doc = editor.state.doc;
                const validPos = Math.min(currentPos, doc.content.size);
                if (validPos > 0) {
                  editor.commands.setTextSelection(validPos);
                }
                editor.commands.focus();
              }
            } catch (error) {
              // Silent error handling
            }
          }, 0);
        }
      }, 0);
    }, 100), // Reduced debounce time for better responsiveness

    extensions: [
      FileHandler.configure({
        allowedMimeTypes: ['image/png', 'image/jpeg', 'image/jpg'],
        onDrop: (currentEditor, files, pos) => {
          files.forEach(file => {
            const fileReader = new FileReader()

            fileReader.readAsDataURL(file)
            fileReader.onload = () => {
              const maxWidth = 600;
              const aspectRatio = 16 / 9;
              const calculatedHeight = maxWidth / aspectRatio;
              currentEditor.chain().insertContentAt(pos, {
                type: 'dBlock',
                content: [
                  {
                    type: 'customImage',
                    attrs: {
                      src: fileReader.result as string,
                      alt: file.name,
                      caption: '',
                      width: maxWidth,
                      height: calculatedHeight,
                    },
                  },
                ],
              }).focus().run();
              setTimeout(() => {
                try {
                  currentEditor.commands.focus(pos + 2);
                } catch {
                  currentEditor.commands.focus();
                }
              }, 10);
            }
          })
        },
        onPaste: (currentEditor, files, htmlContent) => {
          files.forEach(file => {
            // if (htmlContent) {
            //   // if there is htmlContent, stop manual insertion & let other extensions handle insertion via inputRule
            //   // you could extract the pasted file from this url string and upload it to a server for example
            //   console.log(htmlContent) // eslint-disable-line no-console
            //   return false
            // }

            const fileReader = new FileReader()

            fileReader.readAsDataURL(file)
            fileReader.onload = () => {
              const maxWidth = 600;
              const aspectRatio = 16 / 9;
              const calculatedHeight = maxWidth / aspectRatio;
              currentEditor.chain().insertContent({
                type: 'dBlock',
                content: [
                  {
                    type: 'customImage',
                    attrs: {
                      src: fileReader.result as string,
                      alt: file.name,
                      caption: '',
                      width: maxWidth,
                      height: calculatedHeight,
                    },
                  },
                ],
              }).focus().run()
            }
          })
        },
      }),
      // Unified CustomImage extension for all image handling
      CustomImage.configure({
        inline: false,
        allowBase64: true,
        HTMLAttributes: {
          class: 'editor-image',
        },
      }),
      Document,
      DBlock,
      TrailingNode.configure({
        node: 'paragraph',
        notAfter: ['paragraph', 'heading', 'bulletList', 'orderedList'],
      }),
      Paragraph.configure({
        HTMLAttributes: {
          class: 'paragraph-block',
        },
      }),
      Text,
      HardBreak,
      Link.configure({
        autolink: true,
        openOnClick: true,
        linkOnPaste: true,
        protocols: ['http', 'https', 'mailto'],
        HTMLAttributes: {
          class: 'editor-link',
          rel: 'noopener noreferrer',
        },
      }),

      DropCursor.configure({
        color: '#3b82f6',
        width: 2,
      }),
      GapCursor,
      History.configure({
        depth: 100,
        newGroupDelay: 500,
      }),

      // Mark extensions
      Bold,
      Italic,
      Strike,
      Code.configure({
        HTMLAttributes: {
          class: 'inline-code',
        },
      }),

      // Node extensions
      Blockquote.configure({
        HTMLAttributes: {
          class: 'blockquote-block',
        },
      }),
      CodeBlock.configure({
        HTMLAttributes: {
          class: 'code-block',
        },
        languageClassPrefix: 'language-',
      }),

      // Enhanced ListItem with better keyboard navigation
      ListItem.configure({
        HTMLAttributes: {
          class: 'list-item',
        },
      }),

      // Enhanced BulletList for Notion-like behavior
      BulletList.configure({
        HTMLAttributes: {
          class: 'bullet-list',
        },
        itemTypeName: 'listItem',
        keepMarks: true,
        keepAttributes: true,
      }),

      OrderedList.configure({
        HTMLAttributes: {
          class: 'ordered-list',
        },
        itemTypeName: 'listItem',
        keepMarks: true,
        keepAttributes: true,
      }),

      Heading.configure({
        levels: [1, 2, 3],
        HTMLAttributes: {
          class: 'heading-block',
        },
      }),

      HorizontalRule.configure({
        HTMLAttributes: {
          class: 'horizontal-rule',
        },
      }),

      PageLink.configure({
        HTMLAttributes: {
          class: 'page-link',
        },
      }),
    ],
    editable: !disabled,
    editorProps: {
      attributes: {
        class: 'editor',
        'data-testid': 'tiptap-editor',
      },
      handleKeyDown: (_view, event) => {
        // Mark that user is typing for any key that could change content
        if (!['ArrowUp', 'ArrowDown', 'ArrowLeft', 'ArrowRight', 'Home', 'End', 'PageUp', 'PageDown', 'Escape', 'F1', 'F2', 'F3', 'F4', 'F5', 'F6', 'F7', 'F8', 'F9', 'F10', 'F11', 'F12'].includes(event.key)) {
          isTypingRef.current = true;

          // Clear previous timeout
          if (typingTimeoutRef.current) {
            clearTimeout(typingTimeoutRef.current);
          }

          // Set timeout to mark typing as finished
          typingTimeoutRef.current = setTimeout(() => {
            isTypingRef.current = false;
          }, 2000); // Longer timeout for keydown events
        }

        // Let Tiptap handle all keys naturally
        return false;
      },

      // Prevent focus jumping during DOM updates
      handleDOMEvents: {
        focus: (_view, event) => {
          // Only allow focus if it's a genuine user interaction
          if (event.isTrusted) {
            return false; // Let Tiptap handle
          }
          // Prevent programmatic focus changes
          return true;
        },

        blur: (_view, _event) => {
          // Allow natural blur events
          return false;
        },
      },
    },
  });

  // Call onEditorReady when editor is ready
  useEffect(() => {
    if (editor && onEditorReady) {
      onEditorReady(editor);
    }
  }, [editor, onEditorReady]);

  // Add drag & drop functionality directly to editor DOM


  // Cleanup timeout on unmount
  useEffect(() => {
    return () => {
      if (typingTimeoutRef.current) {
        clearTimeout(typingTimeoutRef.current);
      }
    };
  }, []);

  // Update content when prop changes - improved to not interfere with FileHandler
  useEffect(() => {
    if (!editor) return;

    // Prevent updates during user interaction or FileHandler operations
    if (isTypingRef.current || isFileHandlerActiveRef.current) {
      return;
    }

    try {
      const currentJSON = editor.getJSON();

      // Use Tiptap's content validation
      const newContent = content &&
                        typeof content === 'object' &&
                        content.content &&
                        Array.isArray(content.content) &&
                        content.content.length > 0
        ? content
        : defaultContent;

      // Efficient content comparison using Tiptap's JSON structure
      const currentJSONString = JSON.stringify(currentJSON);
      const newContentString = JSON.stringify(newContent);

      if (newContentString !== currentJSONString) {
        // Preserve editor state for better UX
        const currentSelection = editor.state.selection;
        const currentPos = currentSelection.anchor;
        const isEditorFocused = editor.isFocused;

        try {
          // Leverage Tiptap's content validation and normalization
          const normalizedContent = {
            type: 'doc',
            content: Array.isArray(newContent?.content)
              ? newContent.content.filter((item: any) => item?.type)
              : [{ type: 'dBlock', content: [{ type: 'paragraph' }] }]
          };

          // Use Tiptap's schema validation for content structure
          const validatedContent = {
            type: 'doc',
            content: normalizedContent.content.map((item: any) => {
              if (!item?.type) {
                return { type: 'dBlock', content: [{ type: 'paragraph' }] };
              }

              // Ensure dBlock structure is valid for our custom extension
              if (item.type === 'dBlock') {
                return {
                  ...item,
                  content: Array.isArray(item.content) && item.content.length > 0
                    ? item.content
                    : [{ type: 'paragraph' }]
                };
              }

              return item;
            })
          };

          // Use Tiptap's optimized transaction system for content updates
          if (editor && !editor.isDestroyed) {
            // Prevent updates during typing or FileHandler operations
            if (isTypingRef.current || isFileHandlerActiveRef.current) {
              return;
            }

            // Use Tiptap's command system instead of raw transactions to avoid state mismatch
            try {
              // Use setTimeout to avoid flushSync warning and prevent transaction mismatch
              setTimeout(() => {
                if (editor && !editor.isDestroyed) {
                  // Use Tiptap's setContent command which handles state properly
                  const success = editor.commands.setContent(validatedContent, false);

                  if (success) {
                    console.debug('Content updated successfully using commands');

                    // Restore selection if editor was focused
                    if (isEditorFocused && currentPos > 0) {
                      try {
                        const docSize = editor.state.doc.content.size;
                        const validPos = Math.min(currentPos, Math.max(1, docSize - 1));

                        if (validPos > 0 && validPos < docSize) {
                          editor.commands.setTextSelection(validPos);
                        }
                      } catch (selectionError) {
                        console.debug('Selection restoration failed, using default');
                      }
                    }

                    // Restore focus if needed
                    if (isEditorFocused && !editor.isFocused && !isTypingRef.current && !isFileHandlerActiveRef.current) {
                      requestAnimationFrame(() => {
                        if (editor && !editor.isDestroyed && !isTypingRef.current && !isFileHandlerActiveRef.current) {
                          editor.view.focus();
                        }
                      });
                    }
                  } else {
                    console.debug('Content update failed');
                  }
                }
              }, 0);
            } catch (commandError) {
              console.debug('Command failed, content update skipped');
            }
          }

        } catch (error) {
          // Graceful fallback using Tiptap's error recovery
          if (editor && !editor.isDestroyed) {
            const wasEditorFocused = editor.isFocused;

            try {
              // Use Tiptap's transaction system for consistent fallback
              const { state, view } = editor;
              const tr = state.tr;
              tr.setMeta('preventUpdate', true);
              tr.setMeta('addToHistory', false);

              const defaultDoc = state.schema.nodeFromJSON(defaultContent);
              tr.replaceWith(0, state.doc.content.size, defaultDoc);
              view.dispatch(tr);

              // Restore focus using Tiptap's focus system
              if (wasEditorFocused) {
                requestAnimationFrame(() => {
                  if (editor && !editor.isDestroyed && !editor.isFocused) {
                    editor.view.focus();
                  }
                });
              }
            } catch (fallbackError) {
              // Final fallback using Tiptap's command system
              editor.commands.setContent(defaultContent, false);
              if (wasEditorFocused) {
                requestAnimationFrame(() => {
                  editor.commands.focus();
                });
              }
            }
          }
        }
      }
    } catch (error) {
      // Silent error handling to prevent console spam
    }
  }, [content, editor]);


  return (
    <div
      css={{
        marginTop: '20px',
        border: 'none',
        borderRadius: '0px',
        padding: '0px',
        backgroundColor: 'transparent',

        // Editor container styles
        '.editor': {
          outline: 'none',
        },

        // Main Tiptap editor styles
        '.tiptap': {
          fontSize: '16px',
          lineHeight: '1.6',
          color: '#374151',
          minHeight: '200px',
          padding: '0px',
          border: 'none',
          borderRadius: '0px',
          backgroundColor: 'transparent',

          '&:focus': {
            outline: 'none',
          },

          // Ensure drag & drop is enabled
          '&[contenteditable="true"]': {
            cursor: 'text',
          },

          // Notion-like focus styles for better UX
          '.has-focus': {
            borderRadius: '3px',
            backgroundColor: 'rgba(35, 131, 226, 0.07)',
          },

          // Enhanced cursor visibility
          '.ProseMirror-gapcursor:after': {
            borderTop: '1px solid #3b82f6',
            margin: '0 -4px',
          },

          // Drop cursor styling
          '.ProseMirror-dropcursor': {
            borderLeft: '2px solid #3b82f6',
            pointerEvents: 'none',
            position: 'absolute',
          },

          // Drag over visual feedback
          '&.ProseMirror-dragover': {
            backgroundColor: 'rgba(59, 130, 246, 0.05)',
            border: '2px dashed #3b82f6',
            borderRadius: '8px',
          },

          // First element margin reset
          ':first-of-type': {
            marginTop: 0,
          },

          // Enhanced list styling for Notion-like behavior
          'ul, ol': {
            padding: '0 0 0 1.5rem',
            margin: '0.5rem 0',

            'li': {
              marginBottom: '0.25rem',
              position: 'relative',

              'p': {
                marginTop: '0.25em',
                marginBottom: '0.25em',
              },

              // Nested list styling with proper indentation
              'ul, ol': {
                marginTop: '0.25rem',
                marginBottom: '0.25rem',
                paddingLeft: '1.5rem',
              },
            },
          },

          // Bullet list specific styling
          '.bullet-list': {
            listStyleType: 'disc',

            'li': {
              '&::marker': {
                color: '#6b7280',
              },
            },
          },

          // Ordered list specific styling
          '.ordered-list': {
            listStyleType: 'decimal',

            'li': {
              '&::marker': {
                color: '#6b7280',
                fontWeight: '500',
              },
            },
          },
          // Heading styles with proper hierarchy
          'h1, h2, h3, h4, h5, h6': {
            lineHeight: 1.1,
            marginTop: '2.5rem',
            textWrap: 'pretty',
            fontWeight: '600',
          },

          'h1, h2': {
            marginTop: '3.5rem',
            marginBottom: '1.5rem',
          },

          'h1': {
            fontSize: '1.4rem',
          },

          'h2': {
            fontSize: '1.2rem',
          },

          'h3': {
            fontSize: '1.1rem',
          },

          'h4, h5, h6': {
            fontSize: '1rem',
          },

          // Enhanced image styles with unified interaction
          'img, .editor-image': {
            maxWidth: '100%',
            height: 'auto',
            borderRadius: '8px',
            margin: '16px 0',
            display: 'block',
            cursor: 'pointer',
            transition: 'opacity 0.2s ease, transform 0.2s ease',

            '&:hover': {
              opacity: 0.8,
              transform: 'scale(1.02)',
            },

            '&.ProseMirror-selectednode': {
              outline: '2px solid #3b82f6',
              outlineOffset: '2px',
            },
          },

          // Inline code styling
          'code': {
            backgroundColor: 'var(--purple-light)',
            borderRadius: '0.4rem',
            color: 'var(--black)',
            fontSize: '0.85rem',
            padding: '0.25em 0.3em',
            fontFamily: '"JetBrains Mono", "Fira Code", Consolas, monospace',
          },

          // Code block styling
          'pre': {
            background: '#1e1e1e',
            borderRadius: '8px',
            color: '#d4d4d4',
            fontFamily: '"JetBrains Mono", "Fira Code", Consolas, monospace',
            margin: '1.5rem 0',
            padding: '1rem',
            overflow: 'auto',
            fontSize: '14px',
            lineHeight: '1.5',

            'code': {
              background: 'none',
              color: 'inherit',
              fontSize: 'inherit',
              padding: 0,
            },
          },

          '.code-block': {
            background: '#1e1e1e',
            borderRadius: '8px',
            color: '#d4d4d4',
            fontFamily: '"JetBrains Mono", "Fira Code", Consolas, monospace',
            margin: '1.5rem 0',
            padding: '1rem',
            overflow: 'auto',
            fontSize: '14px',
            lineHeight: '1.5',
          },

          // Enhanced blockquote styling
          'blockquote, .blockquote-block': {
            borderLeft: '3px solid #e5e7eb',
            margin: '1.5rem 0',
            paddingLeft: '1rem',
            color: '#6b7280',
            fontStyle: 'italic',
            backgroundColor: '#f9fafb',
            padding: '1rem',
            borderRadius: '0 8px 8px 0',
            position: 'relative',
          },

          // Horizontal rule styling
          'hr, .horizontal-rule': {
            borderTop: '1px solid #e5e7eb',
            margin: '2rem 0',
            border: 'none',
            height: '1px',
            backgroundColor: '#e5e7eb',
          },

          // Enhanced link styling
          'a, .editor-link': {
            color: 'rgb(138, 80, 23)',
            cursor: 'pointer',
            textDecoration: 'underline',
            textDecorationColor: 'rgba(138, 80, 23, 0.3)',
            transition: 'color 0.2s ease, text-decoration-color 0.2s ease',

            '&:hover': {
              color: 'var(--purple-contrast)',
              textDecorationColor: 'var(--purple-contrast)',
            },
          },

          // Paragraph block styling
          '.paragraph-block': {
            margin: '0.5rem 0',
          },

          // Heading block styling
          '.heading-block': {
            margin: '1rem 0',
          },
        },
      }}
    >
      {editor && <CustomBubbleMenu editor={editor} />}
      <EditorContent editor={editor} />
    </div>
  );
}

const CustomBubbleMenu = ({ editor }: { editor: Editor }) => {
  return (
    <BubbleMenu
      editor={editor}
      shouldShow={({ view, state, from, to }) => {
        // Only visible when text is selected (not empty selection) or double clicked
        const { selection } = state;
        const { empty } = selection;

        // Not displayed if selection is empty
        if (empty) return false;

        // Not visible if dragging is in progress
        if (view.dragging) return false;

        // Only visible when text is selected
        return from !== to;
      }}
      tippyOptions={{
        duration: 100,
        placement: 'top',
      }}
    >
      <div
        role="group"
        css={{
          display: 'flex',
          backgroundColor: 'white',
          borderRadius: '8px',
          boxShadow: '0 4px 12px rgba(0, 0, 0, 0.15)',
          border: '1px solid rgba(0, 0, 0, 0.1)',
          padding: '4px',
          gap: '2px',
          flexWrap: 'wrap',
          maxWidth: '400px',
        }}
      >
        {/* Text formatting buttons */}
        <div css={{ display: 'flex', gap: '2px', alignItems: 'center' }}>
          <button
            type="button"
            onClick={() => editor.chain().focus().toggleBold().run()}
            css={{
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              padding: '6px 8px',
              borderRadius: '4px',
              border: 'none',
              borderStyle : "none !important",
              backgroundColor: editor.isActive('bold') ? 'rgba(0, 0, 0, 0.1)' : 'transparent',
              color: editor.isActive('bold') ? '#000' : '#666',
              fontSize: '14px',
              fontWeight: editor.isActive('bold') ? 'bold' : 'normal',
              cursor: 'pointer',
              transition: 'all 0.2s',
              '&:hover': {
                backgroundColor: editor.isActive('bold') ? 'rgba(0, 0, 0, 0.15)' : 'rgba(0, 0, 0, 0.05)',
              },
            }}
            title="Bold (Ctrl+B)"
          >
            <strong>B</strong>
          </button>
          <button
            type="button"
            onClick={() => editor.chain().focus().toggleItalic().run()}
            css={{
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              padding: '6px 8px',
              borderRadius: '4px',
              border: 'none',
              borderStyle : "none !important",
              backgroundColor: editor.isActive('italic') ? 'rgba(0, 0, 0, 0.1)' : 'transparent',
              color: editor.isActive('italic') ? '#000' : '#666',
              fontSize: '14px',
              fontStyle: 'italic',
              cursor: 'pointer',
              transition: 'all 0.2s',
              '&:hover': {
                backgroundColor: editor.isActive('italic') ? 'rgba(0, 0, 0, 0.15)' : 'rgba(0, 0, 0, 0.05)',
              },
            }}
            title="Italic (Ctrl+I)"
          >
            I
          </button>
          {/* Underline button - commented out until extension is installed
          <button
            type="button"
            onClick={() => editor.chain().focus().toggleUnderline().run()}
            css={{
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              padding: '6px 8px',
              borderRadius: '4px',
              border: 'none',
              backgroundColor: editor.isActive('underline') ? 'rgba(0, 0, 0, 0.1)' : 'transparent',
              color: editor.isActive('underline') ? '#000' : '#666',
              fontSize: '14px',
              textDecoration: editor.isActive('underline') ? 'underline' : 'none',
              cursor: 'pointer',
              transition: 'all 0.2s',
              '&:hover': {
                backgroundColor: editor.isActive('underline') ? 'rgba(0, 0, 0, 0.15)' : 'rgba(0, 0, 0, 0.05)',
              },
            }}
            title="Underline (Ctrl+U)"
          >
            U
          </button>
          */}
          <button
            type="button"
            onClick={() => editor.chain().focus().toggleStrike().run()}
            css={{
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              padding: '6px 8px',
              borderRadius: '4px',
              border: 'none',
              borderStyle : "none !important",
              backgroundColor: editor.isActive('strike') ? 'rgba(0, 0, 0, 0.1)' : 'transparent',
              color: editor.isActive('strike') ? '#000' : '#666',
              fontSize: '14px',
              textDecoration: editor.isActive('strike') ? 'line-through' : 'none',
              cursor: 'pointer',
              transition: 'all 0.2s',
              '&:hover': {
                backgroundColor: editor.isActive('strike') ? 'rgba(0, 0, 0, 0.15)' : 'rgba(0, 0, 0, 0.05)',
              },
            }}
            title="Strikethrough"
          >
            S
          </button>
          <button
            type="button"
            onClick={() => editor.chain().focus().toggleCode().run()}
            css={{
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              padding: '6px 8px',
              borderRadius: '4px',
              border: 'none',
              borderStyle : "none !important",
              backgroundColor: editor.isActive('code') ? 'rgba(0, 0, 0, 0.1)' : 'transparent',
              color: editor.isActive('code') ? '#000' : '#666',
              fontSize: '14px',
              fontFamily: 'monospace',
              cursor: 'pointer',
              transition: 'all 0.2s',
              '&:hover': {
                backgroundColor: editor.isActive('code') ? 'rgba(0, 0, 0, 0.15)' : 'rgba(0, 0, 0, 0.05)',
              },
            }}
            title="Code"
          >
            {'</>'}
          </button>
        </div>

        <div css={{ width: '1px', height: '20px', backgroundColor: 'rgba(0, 0, 0, 0.1)', margin: '4px 2px' }} />

        {/* Link button */}
        <button
          type="button"
          onClick={() => {
            const url = window.prompt('Enter URL:');
            if (url) {
              editor.chain().focus().setLink({ href: url }).run();
            }
          }}
          css={{
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            padding: '6px 8px',
            borderRadius: '4px',
            border: 'none',
            borderStyle : "none !important",
            backgroundColor: editor.isActive('link') ? 'rgba(0, 0, 0, 0.1)' : 'transparent',
            color: editor.isActive('link') ? '#000' : '#666',
            fontSize: '14px',
            cursor: 'pointer',
            transition: 'all 0.2s',
            '&:hover': {
              backgroundColor: editor.isActive('link') ? 'rgba(0, 0, 0, 0.15)' : 'rgba(0, 0, 0, 0.05)',
            },
          }}
          title="Add Link"
        >
          🔗
        </button>

        {/* Color picker - commented out until extension is installed
        <div css={{ position: 'relative' }}>
          <button
            type="button"
            onClick={() => {
              // Simple color application - in a real app, you'd use a color picker
              editor.chain().focus().setColor('#3b82f6').run();
            }}
            css={{
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              padding: '6px 8px',
              borderRadius: '4px',
              border: 'none',
              backgroundColor: 'transparent',
              color: '#666',
              fontSize: '14px',
              cursor: 'pointer',
              transition: 'all 0.2s',
              '&:hover': {
                backgroundColor: 'rgba(0, 0, 0, 0.05)',
              },
            }}
            title="Text Color"
          >
            A<span style={{ color: '#3b82f6', fontSize: '10px', marginLeft: '2px' }}>▼</span>
          </button>
        </div>
        */}
      </div>
    </BubbleMenu>
  );
};
