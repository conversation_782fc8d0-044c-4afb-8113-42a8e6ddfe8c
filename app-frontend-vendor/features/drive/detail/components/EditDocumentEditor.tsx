"use client";
import { useState, useEffect, useRef } from "react";
import { Box, Button, Text, TextInput } from "@mantine/core";
import { propcolors } from "styles/colors";
import { notifications } from "@mantine/notifications";
import IconNotiFailed from "public/icons/icon-noti-failed.svg";
import IconNotiSuccess from "public/icons/icon-noti-success.svg";
import { ax } from "../../../../utils/axios";
import { ImagePreview } from "../../document/components/ImagePreview";
import { CustomBreadcrumb } from "../../../../components/breadcrumb";
import useSWR from "swr";
import { TiptapEditor } from "features/drive/document/components/TiptapEditor";


type EditDocumentEditorProps = {
  itemInfo: DriveItemInfo;
  onClose: () => void;
  onSave: () => void;
  portal_id: string;
};

export const EditDocumentEditor: React.FC<EditDocumentEditorProps> = ({
  itemInfo,
  onClose,
  onSave,
  portal_id,
}) => {
  const [title, setTitle] = useState<string>(itemInfo.name || "");
  const [content, setContent] = useState<any>(null); // JSON content from Tiptap

  const [selectedFile, setSelectedFile] = useState<File | null>(null);
  const [thumbnailUrl, setThumbnailUrl] = useState<string | null>(null);
  const [isUploading, setIsUploading] = useState<boolean>(false);
  const [fileError, setFileError] = useState<string | null>(null);

  const editorRef = useRef<any>(null);

  // Parse content and thumbnail - following CONTRIBUTING.md guidelines for useEffect usage
  useEffect(() => {
    // Handle thumbnail path
    if (itemInfo.thumbnail_path) {
      setThumbnailUrl(itemInfo.thumbnail_path);
    }

    // Handle content parsing: always parse as Tiptap JSONContent
    if (!itemInfo.content) {
      setContent(null);
      return;
    }

    try {
      const parsedContent = JSON.parse(itemInfo.content);
      setContent(parsedContent);
    } catch (error) {
      // Handle legacy content format (plain text)
      const basicTiptapJSON = {
        type: 'doc',
        content: [
          {
            type: 'paragraph',
            content: [
              {
                type: 'text',
                text: itemInfo.content
              }
            ]
          }
        ]
      };
      setContent(basicTiptapJSON);
    }
  }, [itemInfo.content, itemInfo.thumbnail_path]); // Only update on content or thumbnail_path change

  const handleFileChange = (file: File | null) => {
    if (!file) {
      setSelectedFile(null);
      setFileError(null);
      return;
    }

    const MAX_THUMBNAIL_SIZE = 3 * 1024 * 1024; // 3MB
    if (file.size > MAX_THUMBNAIL_SIZE) {
      setFileError("ファイルサイズは3MB以下にしてください");
      return;
    }

    setSelectedFile(file);
    setFileError(null);
  };

  const handleRemoveImage = () => {
    setSelectedFile(null);
    setFileError(null);
  };

  const handleThumbnailRemove = () => {
    setThumbnailUrl(null);
    setSelectedFile(null);
    setFileError(null);
  };

  const handleSaveThumbnail = async (file: File): Promise<void> => {
    const formData = new FormData();
    formData.append("thumbnail", file);

    await ax.put(`api/v1/drive/file/${itemInfo.id}/thumbnail`, formData);

    // Refresh thumbnail URL
    setThumbnailUrl(null);
    setSelectedFile(file);
  };

  const handleDeleteThumbnail = async (): Promise<void> => {
    await ax.delete(`api/v1/drive/file/${itemInfo.id}/thumbnail`);

    setThumbnailUrl(null);
    setSelectedFile(null);
  };

  const handleSave = async () => {
    // Validation for content - required
    const isJSONContentEmpty = !content ||
      (content.type === 'doc' &&
       (!content.content ||
        content.content.length === 0 ||
        content.content.every((node: any) =>
          node.type === 'paragraph' &&
          (!node.content || node.content.length === 0)
        )
       )
      );

    if (isJSONContentEmpty) {
      return notifications.show({
        title: "エラー",
        message: "内容を入力してください",
        icon: <IconNotiFailed />,
      });
    }

    if (!selectedFile && !thumbnailUrl) {
      return notifications.show({
        title: "エラー",
        message: "サムネイルを選択してください",
        icon: <IconNotiFailed />,
      });
    }

    // Validation for thumbnail
    if (selectedFile && selectedFile.size >= 3 * 1024 * 1024) {
      return notifications.show({
        title: "エラー",
        message: "3MB以下のサムネイルを選択してください",
        icon: <IconNotiFailed />,
      });
    }

    setIsUploading(true);

    try {
      const requestPayload = {
        name: title.trim() || "名称未設定",
        content: JSON.stringify(content), // Send as JSON string
        type: "normal",
        portal_id: portal_id,
        is_document: true,
      };


      // Update document content and metadata
      await ax.put(`api/v1/drive/file/${itemInfo.id}`, requestPayload);

      // If there's a new thumbnail, upload it separately
      if (selectedFile) {
        const thumbnailFormData = new FormData();
        thumbnailFormData.append("thumbnail", selectedFile);

        await ax.put(`api/v1/drive/file/${itemInfo.id}/thumbnail`, thumbnailFormData);
      }


      notifications.show({
        title: "成功",
        message: "ファイルを保存しました",
        icon: <IconNotiSuccess />,
      });

      onSave();
    } catch (error: any) {
      let errorTitle = "エラー";
      let errorMessage = "ファイルの保存に失敗しました";

      if (error.response?.data?.message) {
        errorMessage = error.response.data.message;
      }

      notifications.show({
        title: errorTitle,
        message: errorMessage,
        icon: <IconNotiFailed />,
      });
    } finally {
      setIsUploading(false);
    }
  };

  const fetcher = (url: string) =>
    ax
      .get(url)
      .then((res) => res.data)
      .catch((_err) => {});

  const { data: portalList } = useSWR("api/v1/drive/portals", fetcher, {
    revalidateOnFocus: false,
  });

  const portal = portalList?.find(
    (item: DrivePortal) => item.id === Number(portal_id),
  );

  const BREADCRUMB_LIST = [
    {
      title: "ホーム",
      href: `/drive${portal_id && `?portal_id=${portal_id}`}`,
      id: "root-nav",
    },
    {
      title: portal?.name,
      href: `/drive${portal_id && `?portal_id=${portal_id}`}`,
      id: "portal-nav-portal",
    },
    {
      title: "名称未設定",
      id: "document",
    },
  ];

  return (
    <Box
      css={{
        overflowY: "scroll",
        scrollbarWidth: "none",
        msOverflowStyle: "none",
        "::-webkit-scrollbar": {
          display: "none"
        },
        header: {
          display: "flex",
          justifyContent: "space-between",
          padding: "10px 20px",
          borderTop: `solid 1px ${propcolors.gray[200]}`,
          borderBottom: `solid 1px ${propcolors.gray[200]}`,
        },
        ".header-button-container": {
          display: "flex",
          gap: "15px",
        },
        ".main": {
          display: "flex"
        }
      }}
    >
      <header>
        <CustomBreadcrumb title="ポータル" list={BREADCRUMB_LIST} />
        <Box className="header-button-container">
          <Button
            variant="default"
            disabled={isUploading}
          >
            キャンセル
          </Button>
          <Button
            onClick={handleSave}
            loading={isUploading}
            disabled={isUploading}
          >
            {isUploading ? "保存中..." : "保存する"}
          </Button>
        </Box>
      </header>

      <Box
        css={{
          width: "60%",
          margin: "0 auto",
          padding: "60px",
          height: "100vh"
        }}
      >
        <ImagePreview
          file={selectedFile || undefined}
          thumbnailPath={itemInfo.thumbnail_path || undefined}
          itemId={itemInfo.id}
          onFileChange={handleFileChange}
          onRemove={handleRemoveImage}
          onThumbnailRemove={handleThumbnailRemove}
          showChangeButton={true}
          showRemoveButton={true}
          enableEditMode={true}
          onSaveThumbnail={handleSaveThumbnail}
          onDeleteThumbnail={handleDeleteThumbnail}
        />
        {fileError && (
          <Text
            css={{
              color: propcolors.red[500],
              marginTop: "8px",
              fontSize: "14px"
            }}
          >
            {fileError}
          </Text>
        )}
        <TextInput
          placeholder="タイトルを入力"
          variant="unstyled"
          value={title}
          onChange={(e) => setTitle(e.target.value)}
          disabled={isUploading}
          css={{
            marginTop: "20px",
            input: {
              border: 'none',
              backgroundColor: 'transparent',
              boxShadow: 'none',
              fontSize: '16px',
              borderStyle: "none !important",
            },
          }}
        />

        <TiptapEditor
          portal_id={portal_id}
          content={content}
          onChange={setContent}
          placeholder="内容を入力してください..."
          disabled={isUploading}
        />

      </Box>
    </Box>
  );
};
