import { notifications } from "@mantine/notifications";
import { RiCloseCircleLine } from "@remixicon/react";
import { AxiosError } from "axios";
import { EditableSelectCell } from "components/data-table/cell/editable";
import { Head } from "components/data-table/head";
import { getProductApprovalId } from "components/data-table/utils/product";
import { LeadDataTableArg } from "features/leads/type/table";
import {
  ComponentProps,
  useCallback,
  useEffect,
  useMemo,
  useState,
} from "react";
import { ax } from "utils/axios";
import { useApprovalStatus } from "../master";
import { useProducts } from "../vendor";
import * as Sentry from "@sentry/nextjs";

export const useProductApprovalColumnsDef = (): LeadDataTableArg["columns"] => {
  const { data: products } = useProducts();
  const { data: approvalStatus } = useApprovalStatus();
  const selectOptions = useMemo(
    () =>
      approvalStatus
        ? Object.keys(approvalStatus).map((key) => ({
            label: approvalStatus[key].name,
            value: key,
          }))
        : [],
    [approvalStatus]
  );
  const updateData = useCallback(
    async (leadId: number, productId: number, value: string | null) => {
      const { data: prev } = await ax.get<LeadProduct[]>(
        `/api/v1/leads/${leadId}/products`
      );
      const product = prev.find((product) => product.product_id === productId);
      if (!product) {
        // product が存在しない場合は事実上ない
        throw new Error("Product not found");
      }
      const updated = {
        negotiation_status: product.negotiation_status,
        approval_status: value,
      };
      try {
        await ax.post(`/api/v1/leads/${leadId}/products/${productId}`, updated);
      } catch (err) {
        const { status } = err as AxiosError;
        if (status !== 200) {
          notifications.show({
            title: "ステータス変更に失敗しました。",
            message:
              "商品が正しく変更されませんでした。もう一度お試しください。",
            icon: <RiCloseCircleLine css={{ color: "#f93832" }} />,
          });
          Sentry.captureException(err);
          return { ok: false };
        }
      }
      return { ok: true };
    },
    []
  );

  return useMemo((): LeadDataTableArg["columns"] => {
    // undefined または空の object の場合は何も表示しない
    if (!products || Object.keys(products).length === 0) {
      return [];
    }
    return (
      (products as Product[])
        .filter((product) => product.product_status === "ENABLED")
        .map((product) => ({
          id: getProductApprovalId(product),
          accessorKey: "NOTE: cell で表示をつくるので accessorKey は使わない",
          accessorFn: (row) => {
            if (!Array.isArray(row.products)) {
              return "";
            }
            return row.products?.find(
              (src) => src.product_id === product.product_id
            )?.sales_status;
          },
          header: (params) => (
            <Head header={params.header}>
              {product.product_name} (承認状況)
            </Head>
          ),
          enableColumnFilter: false,
          enableSorting: false,
          meta: {
            customize: {
              variant: "SELECT",
              selectOptions: selectOptions,
            },
          },
          cell: (params) => {
            const original = params.row.original;
            // products が配列でない場合は何も表示しない
            const initialValue = !Array.isArray(original.products)
              ? ""
              : original.products?.find(
                  (src) => src.product_id === product.product_id
                )?.approval_status;
            const [value, setValue] = useState(initialValue);

            // 初期値が変更されたときに state を更新する
            useEffect(() => {
              setValue(initialValue);
            }, [initialValue]);

            // 販売可能ステータスがtrueの場合のみ編集可能
            // initialValue が undefined の場合は何も表示しない
            return initialValue ? (
              <EditableSelectCellWithState
                initialValue={value ?? ""}
                params={params}
                onChange={(value) =>
                  updateData(original.lead_id, product.product_id, value)
                }
              />
            ) : (
              <></>
            );
          },
        })) ?? []
    );
  }, [products, selectOptions, updateData]);
};

const EditableSelectCellWithState = <T,>(
  props: Omit<ComponentProps<typeof EditableSelectCell<T>>, "onChange"> & {
    onChange: (text: string | null) => Promise<{ ok: boolean }>;
  }
) => {
  // 値の更新に失敗した場合もとに戻すための state
  const [value, setValue] = useState(props.initialValue);
  return (
    <EditableSelectCell
      {...props}
      initialValue={value}
      onChange={async (text) => {
        setValue(text);
        const { ok } = await props.onChange(text);
        if (!ok) {
          setValue(props.initialValue);
        }
      }}
    />
  );
};
