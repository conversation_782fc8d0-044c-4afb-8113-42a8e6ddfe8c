import useSWR from "swr";
import { fetcher } from "utils/axios/fetcher";

export const usePrefectures = () => {
  return useSWR<Prefecture[]>("/api/v1/prefecture", fetcher);
};

export const useContractStatus = () => {
  return useSWR<ContractStatusList>("/api/v1/contract_status", fetcher);
};

export const useLinkStatus = () => {
  return useSWR<LinkStatusList>("/api/v1/link_status", fetcher);
};

export const useLeadStatus = () => {
  return useSWR<LeadStatusList>("/api/v1/lead_status", fetcher);
};

export const useNegotiationStatus = () => {
  return useSWR<NegotiationStatusList>("/api/v1/negotiation_status", fetcher);
};

export const useApprovalStatus = () => {
  return useSWR<ApprovalStatusList>("/api/v1/approval_status", fetcher);
};

export const useLeadCustomForm = () => {
  return useSWR("/api/v1/authenticated_lead_forms", fetcher);
};
