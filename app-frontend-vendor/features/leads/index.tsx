import styled from "@emotion/styled";
import Head from "next/head";
import { propcolors } from "../../styles/colors";
import React, { useEffect, useState, Dispatch, SetStateAction } from "react";
import { ax } from "utils/axios";
import { useRouter } from "next/router";
import { useSessionUser } from "utils/recoil/sessionUserState";
import { usePrevPagePath } from "utils/recoil/navigation/navigationState";
import { useFilterSearchLeads } from "utils/recoil/lead/leadFilterState";
import { Button, Group, Input, Select, Text } from "@mantine/core";
import { NewLead } from "components/Modals/leads/newLead";
import { modals } from "@mantine/modals";
import { notifications } from "@mantine/notifications";
import { useIsMobileByUA } from "utils/hooks/useIsMobile";
import { getApiErrorMessage } from "utils/values/errorMessages";
import {
  SEARCH_CONDITION_ALL_SHOW,
  SEARCH_CONDITION_SETTING_BUTTON_LABEL,
  SEARCH_LEAD_NAME_PLACEHOLDER,
} from "constants/ja/common";
import useSWR from "swr";
import { SEARCH_CONDITION_GET_ALL_ID } from "constants/commonSetting";
import { CustomBreadcrumb } from "components/breadcrumb";
import Link from "next/link";
import {
  useSelectedLeadFilter,
  useSetSelectedLeadFilter,
} from "utils/recoil/filters/selectedLeadFilterState";
import { NewTable } from "./components/new-table";
import { MobileLeads } from "./components/mobile";
import {
  RiArrowDownSFill,
  RiFilterLine,
  RiSearchLine,
  RiCheckboxCircleLine,
  RiCloseCircleLine,
} from "@remixicon/react";
import IconNotiFailed from "../../public/icons/icon-noti-failed.svg";
import IconNotiSuccess from "../../public/icons/icon-noti-success.svg";
import { revalidateFilteredLead } from "./hooks/lead";
import { Table } from "@tanstack/react-table";
import { FilterValue } from "./type/filter";

type PresentationProps = {
  CSVexport: () => void;
  deleteList: (leadIds: number[]) => void;
  filterText: string;
  setFilterText: (e: string) => void;
  filters: { value: string; label: string }[];
  setSelectedFilter: (e: string) => void;
  selectedFilter: string;
  searchPartnerNameDisplayValue: string;
  setSearchPartnerNameDisplayName: Dispatch<SetStateAction<string>>;
  searchCondition: SearchCondition | undefined;
  flashDeleteDone: boolean;
};

const Presentation: React.FC<PresentationProps> = ({
  CSVexport,
  deleteList,
  filters,
  setSelectedFilter,
  selectedFilter,
  searchPartnerNameDisplayValue,
  setSearchPartnerNameDisplayName,
  searchCondition,
  flashDeleteDone,
}) => {
  const sessionUser = useSessionUser();
  const sessionUserRole = sessionUser?.user_role;
  const router = useRouter();
  const pushColumnSetting = () => router.push("settings/columns/leads");
  const isMobile = useIsMobileByUA();
  const [totalSelect, setTotalSelect] = useState<number[]>([]);
  const handleDataCheckboxDelete = (data: Table<Lead>) => {
    const selectedData = data.getSelectedRowModel().rows.map((row) => {
      return row.original.lead_id;
    });
    setTotalSelect(selectedData);
  };
  const clickHandleDelete = () => {
    deleteList(totalSelect);
  };
  return (
    <>
      {isMobile && <MobileLeads />}
      {!isMobile && (
        <>
          <div
            css={{
              display: "flex",
              flexDirection: "column",
              height: "calc(100% - 80px)",
              minWidth: 0,
              minHeight: 0,
            }}
          >
            <Head>
              <title>すべての案件 | PartnerProp</title>
            </Head>
            <header
              css={{
                borderTop: `1px solid ${propcolors.gray[200]}`,
                padding: "16px 24px",
                width: "100%",
                display: "flex",
                justifyContent: "space-between",
                alignItems: "center",
                height: "75px",
              }}
            >
              <CustomBreadcrumb
                title="案件管理"
                list={[{ title: "案件一覧" }]}
              />
              <div css={{ display: "flex", gap: "1rem", height: "42px" }}>
                {totalSelect && totalSelect.length > 0 ? (
                  <Button
                    css={{
                      border: `1px solid ${propcolors.orangeDel}`,
                      color: propcolors.orangeDel,
                      fontSize: "14px",
                      fontWeight: 400,
                      borderRadius: "8px",
                      height: "42px",
                    }}
                    variant="default"
                    onClick={() => clickHandleDelete()}
                    className="del-partner"
                  >
                    案件の削除
                  </Button>
                ) : (
                  <>
                    {sessionUserRole === 1 && (
                      <Link
                        href="/settings/import/lead"
                        css={{
                          border: "1px solid " + propcolors.gray[200],
                          backgroundColor: "white",
                          color: propcolors.blackLight,
                          fontSize: "14px",
                          fontWeight: 400,
                          width: "102px",
                          textAlign: "center",
                          lineHeight: "42px",
                          borderRadius: "8px",
                        }}
                      >
                        インポート
                      </Link>
                    )}
                    <Button
                      onClick={CSVexport}
                      variant="default"
                      css={{
                        border: `1px solid ${propcolors.gray[200]}`,
                        color: propcolors.blackLight,
                        fontSize: "14px",
                        fontWeight: 400,
                        borderRadius: "8px",
                        height: "42px",
                      }}
                    >
                      エクスポート
                    </Button>
                    {sessionUserRole === 1 && (
                      <Button
                        onClick={pushColumnSetting}
                        variant={"default"}
                        css={{
                          border: `1px solid ${propcolors.gray[200]}`,
                          color: propcolors.blackLight,
                          fontSize: "14px",
                          fontWeight: 400,
                          borderRadius: "8px",
                          height: "42px",
                        }}
                      >
                        カスタムカラム設定
                      </Button>
                    )}
                    <NewLead />
                  </>
                )}
              </div>
            </header>
            <>
              <Group
                p={20}
                css={{ borderTop: `1px solid ${propcolors.gray[200]}` }}
              >
                <Input
                  icon={<RiSearchLine css={{ color: "red", width: "18px" }} />}
                  placeholder={SEARCH_LEAD_NAME_PLACEHOLDER}
                  onChange={(e: any) => {
                    const searchValue = e.target.value;
                    // 表示用ValueStateに設定
                    setSearchPartnerNameDisplayName(searchValue);
                  }}
                  value={searchPartnerNameDisplayValue}
                  disabled={selectedFilter !== SEARCH_CONDITION_GET_ALL_ID}
                  style={{ width: 300 }}
                />

                <Select
                  icon={<RiFilterLine css={{ color: "red", width: "18px" }} />}
                  style={{ width: 300 }}
                  data={filters}
                  value={selectedFilter}
                  onChange={(e) => {
                    e && setSelectedFilter(e);
                  }}
                  rightSection={<RiArrowDownSFill />}
                  styles={{ rightSection: { pointerEvents: "none" } }}
                />
                {sessionUserRole === 1 && (
                  <Button
                    variant="default"
                    onClick={() => {
                      router.push("/settings/filters/lead");
                    }}
                    css={{
                      border: `1px solid ${propcolors.gray[200]}`,
                      color: propcolors.blackLight,
                      fontSize: "14px",
                      fontWeight: 400,
                      borderRadius: "8px",
                    }}
                  >
                    {SEARCH_CONDITION_SETTING_BUTTON_LABEL}
                  </Button>
                )}
              </Group>
              <div css={{ flex: 1, minHeight: 0 }}>
                <NewTable
                  searchCondition={searchCondition}
                  searchPartnerName={searchPartnerNameDisplayValue}
                  sendDataCheckBoxDelete={handleDataCheckboxDelete}
                  setSearchPartnerNameDisplayName={
                    setSearchPartnerNameDisplayName
                  }
                  flashDeleteDone={flashDeleteDone}
                />
              </div>
            </>
          </div>
        </>
      )}
    </>
  );
};

const Styled = styled(Presentation)``;

const ModalTitle = styled.div`
  width: 100%;
  text-align: center;
  border-bottom: 1px solid ${propcolors.gray[200]};
  padding-bottom: 1rem;
  top: 1.3rem;
  position: absolute;
  margin: 0 -1rem;
  font-weight: 400;
  font-size: 16px;
  line-height: 27px;
`;
const ModalBody = styled.div`
  font-size: 18px;
  color: ${propcolors.blackLight};
  margin: 25px 10px 0px 10px;
`;
const ModalBottom = styled.div`
  color: ${propcolors.partnerRed};
  font-size: 12px;
  margin: 12px 10px 19px 10px;
`;

export const LeadsPage = () => {
  const [filterText, setFilterText] = useState<string>("");
  // フィルター一覧
  const [filters, setFilters] = useState<{ value: string; label: string }[]>([
    SEARCH_CONDITION_ALL_SHOW,
  ]);
  // 選択中フィルターID
  const selectedLeadFilter = useSelectedLeadFilter();
  const setSelectedLeadFilter = useSetSelectedLeadFilter();
  const prevPage = usePrevPagePath();
  const filterSearchLeads = useFilterSearchLeads();

  const setSearchNameDisplayDefault = (): string => {
    let name = "";
    if (typeof window !== "undefined") {
      // Recoilステートからフィルター情報を取得
      const filterSearch = filterSearchLeads;
      if (filterSearch != null && prevPage === "/leads/[id]") {
        filterSearch.columnFilters.forEach((item) => {
          if (
            item.id === "lead_name" &&
            item.value &&
            typeof item.value === "object" &&
            "value" in item.value
          ) {
            const filterValue = item.value as FilterValue;
            if (filterValue.value) {
              name = filterValue.value;
            }
          }
        });
      }
    }
    return name;
  };
  // 検索ボックス用
  const [searchPartnerNameDisplayValue, setSearchPartnerNameDisplayName] =
    useState(setSearchNameDisplayDefault());

  const CSVexport = () => {
    modals.openConfirmModal({
      title: <ModalTitle>データをエクスポート</ModalTitle>,
      labels: {
        confirm: "エクスポート",
        cancel: "キャンセル",
      },
      size: "640px",
      closeButtonProps: { size: "24px" },
      children: (
        <div>
          <ModalBody>データをExcelファイルにエクスポートしますか？</ModalBody>
          <ModalBottom>
            ※出力結果は、メールアドレス宛に送付されます。
          </ModalBottom>
        </div>
      ),
      onConfirm: () => {
        ax.post(`api/v1/async_export/leads`)
          .then((res) => {
            notifications.show({
              title: "エクスポートリクエストを受け付けました。",
              message:
                "処理が完了したらExcelファイルがメールアドレス宛に送付されます。",
                icon: <RiCheckboxCircleLine css={{ color: "#2DCD6E" }} />,
            });
          })
          .catch((err) => {
            notifications.show({
              title: "エラーが発生しました。",
              message: getApiErrorMessage(err.response.data.message),
              icon: <RiCloseCircleLine css={{ color: "#f93832" }} />,
            });
          });
      },
      confirmProps: {
        sx: {
          width: "284px",
          borderRadius: "8px",
          fontSize: "14px",
          lineHeight: "14px",
          paddingTop: "14px",
          paddingBottom: "14px",
          marginRight: "8px",
          marginBottom: "8px",
          backgroundColor: `${propcolors.black}`,
          color: `${propcolors.white}`,
          height: "auto",
          "&:hover": {
            backgroundColor: `${propcolors.black}`,
          },
        },
      },
      cancelProps: {
        // variant: "outline",
        sx: {
          borderRadius: "8px",
          width: "284px",
          left: "24px",
          paddingTop: "14px",
          paddingBottom: "14px",
          position: "absolute",
          marginBottom: "8px",
          height: "auto",
          lineHeight: "14px",
          fontSize: "14px",
          borderColor: `${propcolors.greyDefault}`,
          backgroundColor: `${propcolors.greyDefault}`,
          color: `${propcolors.white}`,
          "&:hover": {
            backgroundColor: `${propcolors.greyDefault}`,
          },
        },
      },
    });
  };

  const filterFetcher = async (url: string) =>
    ax
      .get(url)
      .then((response) => response.data)
      .catch(() => {});

  const { data: newFilters } = useSWR<SearchCondition[]>(
    "/api/v1/leads/search_conditions",
    filterFetcher
  );

  // 選択されているフィルター情報を取得
  const searchCondition = newFilters?.find((item) => {
    return item.id == Number(selectedLeadFilter);
  });

  // フィルター選択肢を設定
  useEffect(() => {
    if (newFilters) {
      // ソート順に並び替え
      const sortedRecord = newFilters.sort((first, second) => {
        return first.sort_order - second.sort_order;
      });

      const tmpFilters = Array<{ value: string; label: string }>(
        SEARCH_CONDITION_ALL_SHOW
      );

      sortedRecord.map((filter) => {
        tmpFilters.push({ value: filter.id.toString(), label: filter.name });
      });

      const searchCondition = tmpFilters.find((item) => {
        return item.value == selectedLeadFilter;
      });

      if (searchCondition == undefined) {
        setSelectedLeadFilter("0");
      }

      setFilters(tmpFilters);
    }
  }, [newFilters]);

  const setSelectFilter = (e: string) => {
    setSelectedLeadFilter(e);
  };

  const ModalContent = styled.div`
    padding: 10px;
    .title-confirm {
      font-size: 18px;
      font-weight: 400;
      color: ${propcolors.blackLight};
      margin: 25px 0 15px 0;
    }
    .description {
      font-size: 12px;
      font-weight: 400;
      color: var(--Semantic-TEXT_BLACK, #23221e);
    }
  `;

  // テーブルのリセットをトリガーするためのstate
  const [flashDeleteDone, setFlashDeleteDone] = useState(false);
  // Open confirmation modal to delete selected checkbox items.
  const deleteList = (leadIds: number[]) => {
    if (leadIds.length === 0) {
      return;
    }
    modals.openConfirmModal({
      title: <ModalTitle>案件を削除</ModalTitle>,
      size: "640px",
      closeButtonProps: { size: "24px" },
      children: (
        <ModalContent>
          <Text className="title-confirm">選択した案件を削除しますか？</Text>
          <Text className="description" style={{ marginBottom: 15 }}>
            選択した案件のうち、プロダクトの承認ステータスが「未対応」の案件のみが削除されます。
          </Text>
          <Text className="description">変更は取り消すことができません。</Text>
          <Text className="description">本当に実行しますか？。</Text>
        </ModalContent>
      ),
      labels: {
        confirm: "削除する",
        cancel: "キャンセル",
      },
      onConfirm: () => {
        ax.post(`/api/v1/leads/delete_list`, { list_ids: leadIds })
          .then(() => {
            notifications.show({
              title: "案件が削除されました",
              message: "",
              icon: <IconNotiSuccess />,
            });
            revalidateFilteredLead();
            // stateを変更してテーブルのリセットをトリガーする
            setFlashDeleteDone((prev) => !prev);
          })
          .catch((err) => {
            const errorMsg = err.response.data.error;
            notifications.show({
              title: "案件削除に失敗しました",
              message: errorMsg,
              icon: <IconNotiFailed />,
            });
          });
      },
      confirmProps: {
        sx: {
          width: "284px",
          height: "42px",
          right: "10px",
          fontSize: "14px",
          fontWeight: 400,
          marginBottom: "10px",
          borderRadius: "8px",
          backgroundColor: `${propcolors.black}`,
          color: `${propcolors.white}`,
          "&:hover": {
            backgroundColor: `${propcolors.black}`,
          },
        },
      },
      cancelProps: {
        variant: "outline",
        sx: {
          width: "284px",
          height: "42px",
          left: "25px",
          position: "absolute",
          fontSize: "14px",
          fontWeight: 400,
          marginBottom: "10px",
          borderRadius: "8px",
          borderColor: `${propcolors.greyDefault}`,
          backgroundColor: `${propcolors.greyDefault}`,
          color: `${propcolors.white}`,
          "&:hover": {
            backgroundColor: `${propcolors.greyDefault}`,
          },
        },
      },
    });
  };

  return (
    <Styled
      CSVexport={CSVexport}
      filterText={filterText}
      deleteList={deleteList}
      setFilterText={setFilterText}
      filters={filters}
      selectedFilter={selectedLeadFilter}
      setSelectedFilter={setSelectFilter}
      searchPartnerNameDisplayValue={searchPartnerNameDisplayValue}
      setSearchPartnerNameDisplayName={setSearchPartnerNameDisplayName}
      searchCondition={searchCondition}
      flashDeleteDone={flashDeleteDone}
    />
  );
};
