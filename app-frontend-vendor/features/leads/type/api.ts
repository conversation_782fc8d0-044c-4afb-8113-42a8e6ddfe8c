export type FilterBody = {
  page: number;
  page_size: number;
  filters: Filter[];
  sorts: Sort[];
};

export type Filter = {
  field: string;
  operator: Operator;
  value: string;
};

export type Sort = {
  sort_by: string;
  order_by: Order;
};

export type Operator =
  | "INCLUDES"
  | "EXCLUDES"
  | "MATCHES"
  | "EQUALS"
  | "NOTEQUALS"
  | "GREATER_THAN"
  | "LESS_THAN"
  | "STARTS"
  | "ENDS";

export type Order = "ASC" | "DESC";
