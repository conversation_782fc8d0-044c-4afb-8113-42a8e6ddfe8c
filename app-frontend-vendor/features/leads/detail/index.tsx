import styled from "@emotion/styled";
import Head from "next/head";
import { GeneralInfo } from "./components/generalInfo";
import { LeadDetailLayout } from "./layout";
import { useLeadProps, useSetLeadProps } from "utils/recoil/lead/leadState";
import { useRouter } from "next/router";
import { useCallback, useEffect } from "react";
import { ax } from "utils/axios";
import { getMasters } from "utils/axios/getMasters";
import { modals } from "@mantine/modals";
import { ActionIcon, TextInput, Tooltip } from "@mantine/core";
import { Copy16Regular } from "@fluentui/react-icons";
import { notifications } from "@mantine/notifications";
import { useGetDataMasters, useSetDataMasters } from "utils/recoil/dataMasters";

import {
  useLeadProducts,
  useSetLeadProducts,
} from "utils/recoil/lead/leadProductState";
import {
  useSetVendorUserList,
  useVendorUserList,
} from "utils/recoil/vendorUserListState";
import {
  usePartnerUserList,
  useSetPartnerUserList,
} from "utils/recoil/partner/partnerUserListState";
import { Skeleton } from "components/Skeleton";
import IconNotiSuccess from "public/icons/icon-noti-success.svg";
import IconNotiFailed from "public/icons/icon-noti-failed.svg";

type PresentationProps = {
  className?: string;
  leadData: Lead | null;
};

const Presentation: React.FC<PresentationProps> = ({ leadData }) => {
  return (
    <>
      <Head>
        <title>{leadData?.lead_name} | 案件詳細 - PartnerProp</title>
      </Head>
      {leadData ? (
        <GeneralInfo leadData={leadData} />
      ) : (
        <>
          <Skeleton />
        </>
      )}
    </>
  );
};

const Styled = styled(Presentation)``;

export const LeadDetail = () => {
  const leadData = useLeadProps();
  const leadProducts = useLeadProducts();
  const setLeadProducts = useSetLeadProducts();
  const leadProps = useLeadProps();
  const setLeadProps = useSetLeadProps();
  const dataMasters = useGetDataMasters();
  const setDataMasters = useSetDataMasters();
  const vendorUserList = useVendorUserList();
  const setVendorUserList = useSetVendorUserList();
  const partnerUserList = usePartnerUserList();
  const setPartnerUserList = useSetPartnerUserList();
  const router = useRouter();
  const { id } = router.query;
  const lead_id = Number(id);

  useEffect(() => {
    if (!dataMasters) {
      getMasters().then((res) => {
        setDataMasters({
          prefectures: res.prefectures,
          negotiation: res.negotiation,
          contract: res.contract,
          link: res.link,
          lead: res.lead,
          column_permissions: res.column_permissions,
        });
      });
    }
  }, [dataMasters, setDataMasters]);

  useEffect(()=>{
    if (leadProps && leadProps.lead_id !== Number(id)) {
      setLeadProps(null);
    }

    if (id && Number(id) > 0 && !leadProps) {
      ax.get(`/api/v1/leads/${id}`)
        .then((response) => {
          setLeadProps(response.data);
        })
        .catch(() => {
          return;
        });
    }
  }, [leadProps, id]);

  useEffect(()=>{
    if (id && Number(id) > 0) {
      ax.get(`/api/v1/leads/${id}/partner_users`)
        .then((res) => {
          setPartnerUserList(res.data);
        })
        .catch((err) => {
          setPartnerUserList([]);
          return;
        });
    }
  }, [id])

  const fetchVendorUserList = useCallback(() => {
    ax.get(`api/v1/vendor_users`).then((response) => {
      setVendorUserList(response.data);
    });
  }, [setVendorUserList]);

  useEffect(() => {
    if (vendorUserList === null) {
      fetchVendorUserList();
    }
  }, [vendorUserList, fetchVendorUserList]);

  const fetchPartnerUserList = useCallback(() => {
    if (lead_id > 0) {
      ax.get(`/api/v1/leads/${lead_id}/partner_users`)
        .then((res) => {
          setPartnerUserList(res.data);
        })
        .catch(() => {});
    }
  }, [lead_id, setPartnerUserList]);

  useEffect(() => {
    if (partnerUserList === null) {
      fetchPartnerUserList();
    }
  }, [partnerUserList, fetchPartnerUserList]);

  const sharePermalink = async () => {
    ax.get(`/api/v1/leads/${leadData?.lead_id}/permalink`)
      .then((res) => {
        modals.open({
          title: "パーマリンク取得",
          children: (
            <TextInput
              value={res.data ?? ""}
              onFocus={(event) => event.target.select()}
              rightSection={
                <Tooltip label="パーマリンク取得" withArrow position="right">
                  <ActionIcon onClick={() => copyToClipboard(res.data)}>
                    <Copy16Regular />
                  </ActionIcon>
                </Tooltip>
              }
            />
          ),
        });
      })
      .catch((err) => {
        notifications.show({
          title: "パーマリンクが取得できませんでした。",
          message:
            "操作内容をお確かめのうえ、時間をおいて、もう一度お試しください。",
          icon: <IconNotiFailed />,
          autoClose: 5000,
        });
      });
  };

  const copyToClipboard = async (form_url: string) => {
    if (form_url !== null) {
      await navigator.clipboard.writeText(form_url);
      notifications.show({
        title: "コピーしました",
        message: "案件URLをクリップボードにコピーしました",
        icon: <IconNotiSuccess />,
      });
    }
  };

  return (
    <LeadDetailLayout
      leadData={leadProps}
      dataMasters={dataMasters}
      sharePermalink={sharePermalink}
      mobileLeadData={leadData}
    >
      <Styled leadData={leadData} />
    </LeadDetailLayout>
  );
};
