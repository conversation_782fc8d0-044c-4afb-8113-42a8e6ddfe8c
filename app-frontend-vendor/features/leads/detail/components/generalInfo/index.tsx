import styled from "@emotion/styled";
import { formatDate } from "constants/commons";
import { useGetDataMasters } from "utils/recoil/dataMasters";
import { propcolors } from "../../../../../styles/colors";
import { CustomItem } from "./components/customItem";
import { PartnerItem } from "./components/partnerItem";

type GeneralInfoProps = {
  leadData: Lead;
};

type PresentationProps = {
  className?: string;
  prefectureList: string[] | undefined;
} & GeneralInfoProps;

const Presentation: React.FC<PresentationProps> = ({
  className,
  leadData,
  prefectureList,
}) => {
  return (
    <div className={className}>
      <section className="generalinfo-profile backdrop-subtle">
        <PartnerItem
          type="text"
          current={leadData.lead_name}
          label={"案件名"}
          DBlabel={"lead_name"}
          value={leadData.lead_name}
        />
        <PartnerItem
          type={"text"}
          current={leadData.postal_code}
          label={"郵便番号"}
          DBlabel={"postal_code"}
          value={leadData.postal_code}
        />
        <PartnerItem
          type={"select"}
          current={leadData.prefecture_id}
          label={"都道府県"}
          DBlabel={"prefecture_id"}
          value={leadData.prefecture_id}
          placeholder={prefectureList}
        />
        <PartnerItem
          type={"text"}
          current={leadData.address}
          label={"住所"}
          DBlabel={"address"}
          value={leadData.address}
        />
        <PartnerItem
          type={"text"}
          current={leadData.tel}
          label={"代表電話番号"}
          DBlabel={"tel"}
          value={leadData.tel}
        />
        <PartnerItem
          type={"number"}
          current={leadData.number_of_employees}
          label={"従業員数"}
          DBlabel={"number_of_employees"}
          value={leadData.number_of_employees}
        />
        <PartnerItem
          type={"text"}
          current={leadData.url}
          label={"URL"}
          DBlabel={"url"}
          value={leadData.url}
        />
      </section>
      <section>
        <h2 className="generalinfo-heading">追加情報</h2>
        <section className="generalinfo-profile backdrop-subtle">
          {leadData.custom.map((item) => {
            return <CustomItem key={item.column_id} item={item} />;
          })}
        </section>
      </section>
      <section>
        <p className="generalinfo-heading-memo">メモ</p>
        <PartnerItem
          type={"textArea"}
          current={leadData.memo}
          label={""}
          DBlabel={"memo"}
          value={leadData.memo}
        />
      </section>
      <section className="generalinfo-profile backdrop-subtle">
        <PartnerItem
          type={"label"}
          current={formatDate(leadData.created_at)}
          label={"作成日時"}
          DBlabel={"created_at"}
          value={formatDate(leadData.created_at)}
        />
        <PartnerItem
          type={"label"}
          current={formatDate(leadData.updated_at)}
          label={"最終更新日時"}
          DBlabel={"updated_at"}
          value={formatDate(leadData.updated_at)}
        />
      </section>
    </div>
  );
};

const Styled = styled(Presentation)`
  display: grid;
  gap: 1rem;
  padding: 0 1.8rem;
  max-height: calc(100vh - 410px);
  overflow: auto;
  .generalinfo {
    &-heading {
      font-size: 16px;
      font-weight: 400;
      margin-top: 2rem;
      margin-bottom: 1rem;
      color: ${propcolors.blackLight};
    }
    &-heading-memo {
      font-size: 12px;
      font-weight: 400;
      color: ${propcolors.blackLightLabel};
    }
    &-products {
      &-list {
        display: grid;
        grid-gap: 16px;
        grid-template-columns: repeat(5, 1fr);
        grid-auto-rows: auto;
      }
    }
    &-profile {
      display: grid;
      grid-template-columns: repeat(2, 1fr);
      grid-column-gap: 2rem;
      grid-auto-rows: auto;
      @media screen and (max-width: 512px) {
        grid-template-columns: 1fr;
      }
    }
  }
`;

export const GeneralInfo: React.FC<GeneralInfoProps> = ({ leadData }) => {
  const dataMasters = useGetDataMasters();
  const flattenPrefectureList = dataMasters?.prefectures?.map((prefecture) => {
    return prefecture.label;
  });
  return <Styled leadData={leadData} prefectureList={flattenPrefectureList} />;
};
