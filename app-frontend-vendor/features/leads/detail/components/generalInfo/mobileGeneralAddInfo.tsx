import styled from "@emotion/styled";
import { useGetDataMasters } from "utils/recoil/dataMasters";
import { PartnerItem } from "./components/partnerItem";
import { CustomItem } from "./components/customItem";

type GeneralInfoProps = {
  leadData: Lead;
};

type PresentationProps = {
  className?: string;
  prefectureList: string[] | undefined;
} & GeneralInfoProps;

const Presentation: React.FC<PresentationProps> = ({
  className,
  leadData,
  prefectureList,
}) => {
  return (
    <div className={className}>
      {leadData.custom.map((item) => {
        return <CustomItem key={item.column_id} item={item} />;
      })}
      <PartnerItem
        type={"textArea"}
        current={leadData.memo}
        label={"メモ"}
        DBlabel={"memo"}
        value={leadData.memo}
      />
    </div>
  );
};

const Styled = styled(Presentation)`
  display: grid;
  gap: 1rem;
  .generalinfo {
    &-heading {
      font-size: 16px;
      margin-bottom: 1rem;
    }
    &-products {
      &-list {
        display: grid;
        grid-gap: 16px;
        grid-template-columns: repeat(5, 1fr);
        grid-auto-rows: auto;
      }
    }
    &-profile {
      display: grid;
      grid-template-columns: repeat(2, 1fr);
      grid-column-gap: 2rem;
      grid-auto-rows: auto;
      @media screen and (max-width: 512px) {
        grid-template-columns: 1fr;
      }
    }
  }
`;

export const MobileGeneralAddInfo: React.FC<GeneralInfoProps> = ({
  leadData,
}) => {
  const dataMasters = useGetDataMasters();
  const flattenPrefectureList = dataMasters?.prefectures?.map((prefecture) => {
    return prefecture.label;
  });
  return <Styled leadData={leadData} prefectureList={flattenPrefectureList} />;
};
