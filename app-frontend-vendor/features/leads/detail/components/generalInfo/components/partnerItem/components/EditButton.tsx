import React, { useState, useEffect } from "react";
import styled from "@emotion/styled";
import { ax } from "utils/axios";
import { useRouter } from "next/router";
import { propcolors } from "styles/colors";
import { getApiErrorMessage } from "utils/values/errorMessages";
import { useLeadProps, useSetLeadProps } from "utils/recoil/lead/leadState";
import { Textarea, Select, NumberInput, TextInput } from '@mantine/core';
import EditIcon from "public/icons/edit.svg";
import IconSelect from "public/icons/icon-arrow-down.svg"
import { notifications } from "@mantine/notifications";
import IconNotiFailed from "public/icons/icon-noti-failed.svg";

type EditButtonProps = {
  title: string;
  current: string | number | null;
  value: string | number | string[] | number[] | null;
  placeholder?: string[] | number[];
  type: "select" | "text" | "number" | "textArea";
  DBlabel: string;
  onBlur: () => void;
  isEditing: boolean;
  toggleEditing: () => void;
};

type PresentationalProps = {
  className?: string;
  modValue: React.Dispatch<any>;
  title: string;
  current: string | number | null;
  value: string | number | string[] | number[] | null;
  placeholder?: string[] | number[];
  type: "select" | "text" | "number" | "textArea";
  onBlur: () => void;
  isEditing: boolean;
  toggleEditing: () => void;
};

const Presentational: React.FC<PresentationalProps> = ({
  className,
  title,
  current,
  value,
  placeholder,
  type,
  onBlur,
  isEditing,
  modValue,
  toggleEditing,
}) => (
  <div className={className}>
    {!isEditing && (
      <EditIcon onClick={toggleEditing} />
    )}
    {isEditing && (
      <div>
        {type === "text" && (
          <TextInput
            className="width-max"
            onBlur={onBlur}
            onChange={(e) => modValue(e.currentTarget.value)}
            type="text"
            autoFocus
            defaultValue={current ? current : ""}
          />
        )}
        {type === "number" && (
          <NumberInput
            className="width-max"
            onBlur={onBlur}
            onChange={(value) => modValue(value)}
            type="number"
            autoFocus
            defaultValue={current ? Number(current) : 0}
            hideControls
          />
        )}
        {type === "select" && (
          <Select
            className="width-max"
            data={
              placeholder
                ? placeholder.map((item, index) => {
                  return { value: value && typeof value === "object" ? String(value[index]) : String(index), label: `${placeholder ? placeholder[index] : item}` };
                })
                : []
            }
            defaultValue={typeof current === "number" || typeof current === "string" ? String(current) : undefined}
            onChange={(e) => modValue(e)}
            onBlur={onBlur}
            rightSection={<IconSelect />}
            autoFocus
            searchable
            clearable
          />
        )}
        {
          type === "textArea" &&
          <Textarea
            className="width-max"
            onBlur={onBlur}
            autoFocus
            onChange={(e) => modValue(e.currentTarget.value)}
            defaultValue={current ? current.toString() : ""}
            maxLength={255}
            placeholder="255文字まで入力してください。"
          />
        }
      </div>
    )}
  </div>
);

const Styled = styled(Presentational) <{ isEditing: boolean }>`
${props => !props.isEditing ? `
  text-align: end;
  .Button {
    border: 0;
  }
  .width-max {
    width: 100%;
  }
  ` : `
  width: 100%;
  text-align: end;
  .Button {
    border: 0;
  }
  .width-max {
    width: 100%;
  }
  `}
  .mantine-Select-item[data-selected] {
      background-color: ${propcolors.gray[150]};
      color: ${propcolors.blackLight};
  }
  .mantine-Input-input {
    border: 1px solid ${propcolors.gray[200]};
    background-color: white;
    border-radius: 8px;
  }
  .mantine-Input-input:focus {
    background-color: ${propcolors.gray[150]};
  }
`;

export const EditButton: React.FC<EditButtonProps> = ({
  type,
  title,
  value,
  current,
  placeholder,
  DBlabel,
  onBlur,
  isEditing,
  toggleEditing,
}) => {
  const [modValue, setModValue] = useState<string | number>();
  const leadData = useLeadProps();
  const setLeadData = useSetLeadProps();
  const router = useRouter();
  const { id } = router.query;
  const onSubmit = async () => {
    const body = {
      ...leadData,
      [DBlabel]: modValue,
    };

    ax.put(`/api/v1/leads/${id}`, body)
      .then((res) => {
        fetchLeadProps();
        toggleEditing();
      })
      .catch ((err) => {
        toggleEditing(); // Close editing mode even if there's an error
        notifications.show({
          icon: <IconNotiFailed />,
          title: "案件の更新に失敗しました",
          message: getApiErrorMessage(err.response.data.message),
          autoClose: 5000,
        });
      });
  };

  const fetchLeadProps = async () => {
    const res = await ax.get(`/api/v1/leads/${id}`);
    setLeadData(res.data);
  };

  useEffect(() => {
    if (!isEditing) {
      setModValue(current ?? '');
    }
  }, [isEditing, current]);

  return (
    <Styled
      type={type}
      title={title}
      current={current}
      value={value}
      placeholder={placeholder}
      modValue={setModValue}
      onBlur={() => {
        onSubmit();
        onBlur();
      }}
      toggleEditing={toggleEditing}
      isEditing={isEditing}
    />
  );
};
