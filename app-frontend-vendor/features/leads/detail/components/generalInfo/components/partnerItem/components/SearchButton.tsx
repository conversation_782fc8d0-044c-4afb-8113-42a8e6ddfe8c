import React, { useEffect, useState } from "react";
import styled from "@emotion/styled";
import { ax } from "utils/axios";
import { useRouter } from "next/router";
import { propcolors } from "styles/colors";
import { notifications } from "@mantine/notifications";
import { useLeadProps, useSetLeadProps } from "utils/recoil/lead/leadState";
import EditIcon from "public/icons/edit.svg";
import { getApiErrorMessage } from "utils/values/errorMessages";
import { Select } from "@mantine/core";
import IconNotiFailed from "public/icons/icon-noti-failed.svg";
import IconSelect from "public/icons/icon-arrow-down.svg";

type SearchButtonProps = {
  title: string | number;
  current: number;
  userList: User[] | Lead[] | null;
  DBlabel: string;
  filterList: (keyword: string) => User[] | Lead[] | null;
  type: string;
  onBlur: () => void;
  isEditing: boolean;
  toggleEditing: () => void;
};

type PresentationalProps = {
  className?: string;
  modValue: number;
  setModValue: React.Dispatch<React.SetStateAction<number>>;
  title: string | number;
  current: number;
  userList: User[] | Lead[] | null;
  onBlur: () => void;
  isEditing: boolean;
  toggleEditing: () => void;
  filter: (keyword: string) => void;
};

const Presentational: React.FC<PresentationalProps> = ({
  className,
  title,
  current,
  isEditing,
  userList,
  modValue,
  setModValue,
  onBlur,
  toggleEditing,
  filter,
}) => {
  const options = [
    { value: "-1", label: "担当者なし" },
    ...(userList
      ? userList.map((user, index) => ({
          value: String(index),
          label: `${(user as Lead).partner_name || ""} ${(user as User).name || ""} ${(user as User).email || ""}`,
        }))
      : []),
  ];

  return (
    <div className={className}>
      {!isEditing && <EditIcon onClick={toggleEditing} className="button" />}
      {isEditing && (
        <>
          <Select
            className="width-max"
            placeholder="氏名もしくはメールアドレスで検索"
            onBlur={onBlur}
            onChange={(value) => setModValue(Number(value))}
            name="select"
            data={options}
            value={String(modValue)}
            rightSection={<IconSelect />}
            autoFocus
            searchable
          />
        </>
      )}
    </div>
  );
};

const Styled = styled(Presentational)`
  .Button {
    border: 0;
  }
  .button {
    cursor: pointer;
  }
  .editor {
    &-content {
      width: 500px;
    }
    &-input {
      width: 300px;
    }
  }
  .mantine-Select-item[data-selected] {
    background-color: ${propcolors.gray[150]};
    color: ${propcolors.blackLight};
  }
  .mantine-Input-input {
    border: 1px solid ${propcolors.gray[200]};
    background-color: white;
    border-radius: 8px;
  }
  .mantine-Input-input:focus {
    background-color: ${propcolors.gray[150]};
  }
`;

export const SearchButton: React.FC<SearchButtonProps> = ({
  title,
  current,
  userList,
  DBlabel,
  filterList,
  type,
  onBlur,
  isEditing,
  toggleEditing,
}) => {
  const [isModalOpen, toggleModal] = useState<boolean>(false);
  const [modValue, setModValue] = useState<number>(-1);
  const [keywordValue, setKeyWordValue] = useState<string | number>();
  const [filteredUser, setFilteredUser] = useState<User[] | Lead[] | null>(
    userList
  );
  const leadData = useLeadProps();
  const setLeadData = useSetLeadProps();
  const router = useRouter();
  const { id } = router.query;
  const onSubmit = async () => {
    let userId = null;
    if (
      modValue !== -1 &&
      filteredUser &&
      type === "USER" &&
      filteredUser.length > modValue
    ) {
      userId = (filteredUser as User[])[modValue].id;
    }

    const body = {
      ...leadData,
      [DBlabel]: userId,
    };
    ax.put(`/api/v1/leads/${id}`, body)
      .then((res) => {
        fetchLeadProps();
        toggleEditing();
      })
      .catch((err) => {
        notifications.show({
          icon: <IconNotiFailed />,
          title: "案件担当者の更新ができませんでした。",
          message: getApiErrorMessage(err.response.data.message),
          autoClose: 5000,
        });
      });
  };
  const fetchLeadProps = async () => {
    ax.get(`/api/v1/leads/${id}`).then((res) => {
      setLeadData(res.data);
    });
  };
  useEffect(() => {
    setFilteredUser(userList);
  }, [userList]);
  const filter = (keyword: string) => {
    setFilteredUser(filterList(keyword));
  };

  useEffect(() => {
    if (userList && current) {
      const index = userList.findIndex((user) => (user as User).id === current);
      setModValue(index !== -1 ? index : -1);
    }
  }, [current, userList, isEditing]);

  const toggle = () => {
    toggleModal(!isModalOpen);
  };

  // モーダル開閉時に値を初期化
  useEffect(() => {
    setModValue(-1);
  }, [isModalOpen]);
  return (
    <Styled
      title={title}
      current={current}
      userList={filteredUser}
      modValue={modValue}
      setModValue={setModValue}
      onBlur={() => {
        onSubmit();
      }}
      toggleEditing={toggleEditing}
      isEditing={isEditing}
      filter={filter}
    />
  );
};
