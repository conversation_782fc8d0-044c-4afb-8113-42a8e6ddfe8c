import styled from "@emotion/styled";
import { Delete16Filled } from "@fluentui/react-icons";
import { ActionIcon, Select } from "@mantine/core";
import { Content, List, Root, Trigger } from "@radix-ui/react-tabs";
import Image from "next/image";
import Link from "next/link";
import { useRouter } from "next/router";
import IconPartner from "public/icons/partner-icon.svg";
import IconUserPartner from "public/icons/user-icon.svg";
import { useEffect, useState } from "react";
import { propcolors } from "styles/colors";
import { ax } from "utils/axios";
import { EditButton } from "./../../components/generalInfo/components/partnerItem/components/EditButton";
import { SearchButton } from "../generalInfo/components/partnerItem/components/SearchButton";
import { MobileGeneralAddInfo } from "../generalInfo/mobileGeneralAddInfo";
import { MobileGeneralInfo } from "../generalInfo/mobileGeneralInfo";
import { SubtableMobileTabView } from "../subTable/subtableMobileTabView";

type SubTableItem = {
  value: string;
  label: string;
};

type PresentationProps = {
  deleteDetail: () => Promise<void>;
  sharePermalink: () => Promise<void>;
  userRole: number;
  leadDataProps: Lead | null;
  leadData: Lead | null;
  linkedPartnerUserList: User[] | null;
  filterLinkedPartnerUserList: (keyword: string) => User[] | null;
  dataMasters: DataMasters;
  subtableList: Subtable[] | null;
};
const Presentation: React.FC<PresentationProps> = ({
  deleteDetail,
  userRole,
  leadData,
  filterLinkedPartnerUserList,
  linkedPartnerUserList,
  dataMasters,
  leadDataProps,
  subtableList,
}) => {
  // 表示サブテーブル用state
  const [selectedTable, setSelectedTable] = useState<string>();
  const [subtableData, setSubtableData] = useState<SubtableRecord | null>(null);
  const { query } = useRouter();
  // 表示サブテーブルの選択肢
  let convertedTableItems: SubTableItem[] = [];
  if (subtableList) {
    subtableList.map((item) => {
      let tmpItem = {} as SubTableItem;
      tmpItem.value = item.sub_table_id.toString();
      tmpItem.label = item.sub_table_name;
      convertedTableItems.push(tmpItem);
    });
  }

  // 表示サブテーブルの変更を反映
  const changeSubTableSelect = (value: string | null) => {
    if (value) {
      setSelectedTable(value);
    }
  };

  // 表示サブテーブルの選択肢生成後に初期値を設定
  useEffect(() => {
    if (subtableList && subtableList.length > 0) {
      setSelectedTable(subtableList[0].sub_table_id.toString());
    }
  }, [subtableList]);

  // 選択サブテーブルが変更された場合
  useEffect(() => {
    if (query.id && selectedTable) {
      // 初期化
      setSubtableData(null);
      ax.get(`/api/v1/lead_sub_table/${selectedTable}/leads/${query.id}`).then(
        (res) => {
          setSubtableData(res.data);
        },
      );
    }
  }, [selectedTable, query.id]);

  const [isEditing, setIsEditing] = useState(false);
  const toggleEditing = () => {
    setIsEditing(!isEditing);
  };

  const [isEditingUsername, setIsEditingUsername] = useState(false);
  const toggleEditingUsername = () => {
    setIsEditingUsername(!isEditingUsername);
  };

  return (
    <>
      {leadDataProps && leadData && (
        <div>
          <section className="header-button-container">
            <Link className="sidebar-information-title_back" href="/leads">
              ← 一覧に戻る
            </Link>
            {userRole === 1 && (
              <ActionIcon variant="subtle" color="gray" onClick={deleteDetail}>
                <Delete16Filled />
                レコード削除
              </ActionIcon>
            )}
          </section>

          <Root className="TabsRoot" defaultValue="tab1">
            <List className="TabsList" aria-label="Manage your account">
              <Trigger className="TabsTrigger" value="tab1">
                契約情報
              </Trigger>
              <Trigger className="TabsTrigger" value="tab2">
                基本情報
              </Trigger>
              <Trigger className="TabsTrigger" value="tab3">
                追加情報
              </Trigger>
              {convertedTableItems && convertedTableItems.length > 0 && (
                <Trigger className="TabsTrigger" value="tab4">
                  サブテーブル
                </Trigger>
              )}
            </List>
            <Content className="TabsContent tab-card" value="tab1">
              <section className="sidebar-information-title">
                <p className="sidebar-information-title_id">
                  <span className="sidebar-information-title_id_heading">
                    レコードID
                  </span>
                  {leadDataProps.hex_record_id}
                </p>
                <p className="sidebar-information-title_name">
                  {leadDataProps.lead_name}
                </p>
                {leadDataProps.url && (
                  <Link
                    href={leadDataProps.url}
                    target="_blank"
                    className="sidebar-information-title_url"
                    style={{ width: "100%", wordBreak: "break-all" }}
                  >
                    {leadDataProps.url}
                  </Link>
                )}

                <p className="sidebar-information-title_status_heading">
                  共有先パートナー企業名
                </p>
                <div className="sidebar-information-title_status">
                  <p className="sidebar-information-title_status_name">
                    {leadDataProps.partner_id ? (
                      <div className="infomation-partner">
                        {leadDataProps.partner_logo_url ? (
                          <Image
                            src={leadDataProps.partner_logo_url}
                            style={{
                              borderRadius: "40px",
                              marginRight: "1rem",
                            }}
                            width={40}
                            height={40}
                            alt={leadDataProps?.partner_name || ""}
                          />
                        ) : (
                          <IconPartner className="imgSpan" />
                        )}
                        {leadDataProps.partner_name}
                      </div>
                    ) : (
                      <span>未設定</span>
                    )}
                  </p>
                </div>
                <p className="sidebar-information-title_status_heading">
                  パートナー担当者
                </p>

                <div className="sidebar-information-title_status">
                  {!isEditingUsername &&
                    (leadData.partner_user_id ? (
                      <p className="sidebar-information-title_status_name">
                        <div className="infomation-partner">
                          {leadData?.partner_avatar_url ? (
                            <Image
                              src={leadData.partner_avatar_url}
                              style={{
                                borderRadius: "40px",
                                marginRight: "1rem",
                              }}
                              width={40}
                              height={40}
                              alt={leadData?.partner_user_name || ""}
                            />
                          ) : (
                            <IconUserPartner className="imgSpan" />
                          )}
                          <p className="right-name">
                            {leadData.partner_user_name}
                          </p>
                        </div>
                      </p>
                    ) : (
                      <p className="sidebar-information-title_status_name">
                        <div className="infomation-partner">
                          <IconUserPartner className="imgSpan" />
                          <p className="right-name">未設定</p>
                        </div>
                      </p>
                    ))}

                  <div
                    className={
                      isEditingUsername ? "right-icon-full" : "right-icon"
                    }
                  >
                    <SearchButton
                      title={
                        leadData.partner_user_name
                          ? leadData.partner_user_name
                          : "未設定"
                      }
                      current={
                        leadData.partner_user_id ? leadData.partner_user_id : -1
                      }
                      userList={linkedPartnerUserList}
                      DBlabel={"partner_user_id"}
                      filterList={filterLinkedPartnerUserList}
                      type={"USER"}
                      onBlur={toggleEditingUsername}
                      isEditing={isEditingUsername}
                      toggleEditing={toggleEditingUsername}
                    />
                  </div>
                </div>
              </section>
              <div className="sidebar-line"></div>
              <section className="sidebar-information-content no-backdrop">
                <p className="sidebar-status-link_heading">連携ステータス</p>
                <div className="sidebar-information-content_card_body">
                  <p className="sidebar-information-content_card_status">
                    {!isEditing &&
                      (leadData.lead_status ? (
                        <span>{leadData.lead_status_name}</span>
                      ) : (
                        <span>連携ステータスがありません</span>
                      ))}
                    <EditButton
                      title={"案件ステータス"}
                      type={"select"}
                      current={leadData.lead_status}
                      DBlabel={"lead_status"}
                      onBlur={toggleEditing}
                      isEditing={isEditing}
                      toggleEditing={toggleEditing}
                      value={
                        dataMasters.lead
                          ? Object.keys(dataMasters.lead)
                          : [
                              "LEAD_PENDING",
                              "LEAD_APPROACHING",
                              "LEAD_APPOINTMENT",
                              "LEAD_IN_PROGRESS",
                              "LEAD_PIC_AGREEMENT",
                              "LEAD_APPROVER_AGREEMENT",
                              "LEAD_APPLIED",
                              "LEAD_ON_HOLD",
                              "LEAD_FAILURE",
                              "LEAD_TERMINATION",
                            ]
                      }
                      placeholder={[
                        "未対応",
                        "アプローチ中",
                        "アポ設定",
                        "商談中",
                        "担当者合意",
                        "決裁者合意",
                        "申込済",
                        "保留",
                        "不成立",
                        "解約",
                      ]}
                    />
                  </p>
                </div>
              </section>
            </Content>
            {leadData && (
              <>
                <Content className="TabsContent tab-card" value="tab2">
                  <MobileGeneralInfo leadData={leadData} />
                </Content>
                <Content className="TabsContent tab-card" value="tab3">
                  <MobileGeneralAddInfo leadData={leadData} />
                </Content>
              </>
            )}
            <Content
              className="TabsContent tab-card"
              value="tab4"
              style={{ overflow: "unset" }}
            >
              <section className="" style={{ maxHeight: "75vh" }}>
                <div>
                  {convertedTableItems && (
                    <Select
                      value={selectedTable}
                      defaultValue={selectedTable}
                      label="表示サブテーブル"
                      data={convertedTableItems}
                      onChange={(e) => changeSubTableSelect(e)}
                    />
                  )}
                </div>
                {subtableData && selectedTable ? (
                  <SubtableMobileTabView
                    records={subtableData}
                    selectedSubTableId={selectedTable}
                  />
                ) : (
                  "データ取得中"
                )}
              </section>
            </Content>
          </Root>
          <style>{`
      .TabsList {
        flex-shrink: 0;
        display: flex;
        border-bottom: 1px solid var(--mauve-6);
      }
      .TabsTrigger {
        font-family: inherit;
        background-color: white;
        padding: 0 10px;
        height: 45px;
        flex: 1;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 15px;
        line-height: 1;
        color: var(--mauve-11);
        user-select: none;
        font-size: 14px;
      }
      .TabsTrigger:first-child {
        border-top-left-radius: 6px;
      }
      .TabsTrigger:last-child {
        border-top-right-radius: 6px;
      }
      .TabsTrigger:hover {
        color: var(--violet-11);
      }
      .TabsTrigger[data-state='active'] {
        color: var(--violet-11);
        border-bottom: 4px solid #e0322d;
      }
      .TabsContent {
        padding: 25px;
        margin-bottom: 5rem;
      }
      .TabsContent .tab-card {
        padding: 1rem;
        background-color: ${propcolors.white};
        border: 1px solid ${propcolors.gray[200]};
        border-radius: 0.5rem;
        filter: drop-shadow(0px 0px 4px ${propcolors.gray[300]}80);
        overflow-x: hidden;
        max-width: 100%;
      }
      .header-button-container {
        display: flex;
        justify-content: space-between;
        padding: 0.5rem 0.5rem 0.5rem 1rem;
        button {
          display: inline-block;
          width: auto;
          text-align: center;
        }
      }
      .TabsContent .sidebar {
        width: 100%;
        height: 800px;
        overflow: auto;
        display: grid;
        grid-template-rows: auto 1fr;
        @media screen and (max-width: 512px) {
          width: 100vw;
        }
      }
      .TabsContent .sidebar-line {
        border-top: 1px solid ${propcolors.gray[200]};
      }
      .TabsContent .sidebar-information {
        color: ${propcolors.blackLightLabel};
        padding: 0;
      }
      .TabsContent .sidebar-information-url_heading {
        font-weight: 400;
        font-size: 12px;
        color: ${propcolors.blackLightLabel};
      }
      .TabsContent .sidebar-information-title {
        display: grid;
        grid-template-rows: auto auto auto;
        padding-bottom: 25px;
      }
      .TabsContent .sidebar-information-title_id {
        font-size: 14px;
        font-weight: 400;
        color: ${propcolors.blackLightLabel};
      }
      .TabsContent .sidebar-information-title_id_heading {
        font-size: 14px;
        margin-bottom: 5px;
        color: ${propcolors.blackLightLabel};
        margin-right: 8px;
      }
      .TabsContent .sidebar-information-title_name {
        font-size: 18px;
        font-weight: 400;
        color: ${propcolors.blackLight};
      }
      .TabsContent .sidebar-information-title_url {
        font-weight: 400;
        font-size: 14px;
        color: ${propcolors.blackLight};
        display: inline-block;
        width: min-content;
        text-decoration: underline;
        margin-top: 1rem;
      }
      .TabsContent .sidebar-information-title_status {
        display: flex;
        align-items: center;
      }
      .TabsContent .sidebar-information-title_status_heading {
        margin-top: 20px;
        margin-bottom: 16px;
        font-weight: 400;
        font-size: 12px;
        color: ${propcolors.blackLightLabel};
      }
      .TabsContent .sidebar-information-title_status_name {
        margin-right: 10px;
        width: 100%;
        font-weight: 400;
        font-size: 14px;
        color: ${propcolors.blackLight};
      }
      .TabsContent .sidebar-information-content {
        padding-top: 25px;
      }
      .TabsContent .sidebar-information-content_card_heading {
        font-size: 12px;
        font-weight: 400;
        margin-bottom: 16px;
        color: ${propcolors.blackLightLabel};
      }
      .TabsContent .sidebar-information-content_card_body {
        display: flex;
        justify-content: space-between;
        font-size: 1rem;
        font-weight: bold;
        margin-bottom: 25px;
      }
      .TabsContent .sidebar-information-content_card_message-top {
        margin-top: 6px;
        color: ${propcolors.blackLight};
        font-weight: 400;
        font-size: 16px;
      }
      .TabsContent .sidebar-information-content_card_status {
        display: flex;
        align-items: center;
        gap: 10px;
        width: 100%;
      }
      .TabsContent .sidebar-information-content_card_status span {
        width: 100%;
        color: ${propcolors.blackLight};
        font-weight: 400;
        font-size: 16px;
      }
      .TabsContent .sidebar-products {
        width: 100%;
        font-size: 0.875rem;
        border: 1px solid ${propcolors.gray[200]};
        border-radius: 5px;
        background-color: ${propcolors.white};
      }
      .TabsContent .sidebar-products_heading {
        padding: 0.7rem 25px 0.7rem 25px;
        background-color: ${propcolors.greyBreadcrumb};
        color: ${propcolors.blackLightLabel};
        border-top: 1px solid ${propcolors.gray[200]};
        font-weight: 400;
        font-size: 14px;
      }
      .TabsContent .sidebar-products-notfound {
        padding: 25px;
        font-size: 14px;
        font-weight: 400;
        color: ${propcolors.blackLight};
      }
      .infomation-partner {
        display: flex;
        align-items: center;
      }
    `}</style>
        </div>
      )}
    </>
  );
};

const Styled = styled(Presentation)`
`;

export const LeadDetailMobileTabView: React.FC<PresentationProps> = ({
  deleteDetail,
  sharePermalink,
  userRole,
  leadData,
  filterLinkedPartnerUserList,
  linkedPartnerUserList,
  dataMasters,
  leadDataProps,
  subtableList,
}) => {
  return (
    <Styled
      deleteDetail={deleteDetail}
      sharePermalink={sharePermalink}
      userRole={userRole}
      leadData={leadData}
      linkedPartnerUserList={linkedPartnerUserList}
      filterLinkedPartnerUserList={filterLinkedPartnerUserList}
      dataMasters={dataMasters}
      leadDataProps={leadDataProps}
      subtableList={subtableList}
    />
  );
};
