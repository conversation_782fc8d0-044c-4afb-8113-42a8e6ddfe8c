type CustomColumnContents = ({
  column_id: number;
} & (
  | {
      custom_column_content: string | number | null;
    }
  | {
      select_id: number | null;
    }
  | {
      vendor_user_id: number | null;
    }
  | {
      partner_user_id: number | null;
    }
))[];

export const customColumnContentsMap = (
  custom: CustomColumn[],
  target: { customColumnId: number; value: string | number | null }
): CustomColumnContents => {
  return custom.map((column) => {
    const {
      id,
      select_id,
      vendor_user_id,
      partner_user_id,
      current,
      type_status,
    } = column;
    const isTarget = target.customColumnId === column.id;
    switch (type_status) {
      case "SELECT":
        return {
          column_id: id,
          select_id: isTarget
            ? target.value
              ? Number(target.value)
              : null
            : select_id,
        };
      case "VENDOR_USER":
        return {
          column_id: id,
          vendor_user_id: isTarget
            ? target.value
              ? Number(target.value)
              : null
            : vendor_user_id,
        };
      case "PARTNER_USER":
        return {
          column_id: id,
          partner_user_id: isTarget
            ? target.value
              ? Number(target.value)
              : null
            : partner_user_id,
        };
      default:
        return {
          column_id: id,
          custom_column_content: isTarget
            ? target.value
              ? target.value
              : null
            : current,
        };
    }
  });
};
