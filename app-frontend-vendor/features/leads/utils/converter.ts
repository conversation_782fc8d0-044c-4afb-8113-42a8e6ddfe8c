import { ColumnFiltersState, SortingState } from "@tanstack/react-table";
import { FilterBody } from "../type/api";
import { CustomColumnFiltersState, FilterValue } from "../type/filter";
import dayjs from "dayjs";

type Filter = Omit<CustomColumnFiltersState[number], "value"> & {
  value: Omit<FilterValue, "value"> & { value: string };
};

const nonNullValue = (
  filter: CustomColumnFiltersState[number]
): filter is Filter => !!filter.value?.value;

export const filterMap = (
  columnFilters: ColumnFiltersState
): FilterBody["filters"] => {
  return (columnFilters as CustomColumnFiltersState)
    .filter(nonNullValue)
    .map((filter) => {
      const { operator } = filter.value;
      let value = filter.value.value;
      if (filter.id === "created_at" || filter.id === "updated_at") {
        value = dayjs(value).format("YYYY-MM-DD HH:mm:ss");
      }

      return {
        operator,
        field: filter.id,
        value,
      };
    });
};

export const sortingMap = (sorting: SortingState): FilterBody["sorts"] => {
  return sorting.map((sort) => ({
    sort_by: sort.id,
    order_by: sort.desc ? "DESC" : "ASC",
  }));
};
