import { customColumnContentsMap } from "./custom";

describe("customColumnContentsMap", () => {
  const customColumns = [
    {
      id: 1,
      vendor_id: 1,
      column_name: "name1",
      column_label: "label1",
      current: "current1",
      type: 3,
      type_status: "SELECT",
      type_status_name: "select",
      select_id: 1,
      select_contents: [
        { select_id: 1, column_id: 1, select_value: "value1", sort_order: 0 },
        { select_id: 2, column_id: 1, select_value: "value2", sort_order: 1 },
        { select_id: 3, column_id: 1, select_value: "value3", sort_order: 2 },
        { select_id: 4, column_id: 1, select_value: "value4", sort_order: 3 },
        { select_id: 5, column_id: 1, select_value: "value5", sort_order: 4 },
      ],
    },
    {
      id: 2,
      vendor_id: 1,
      column_name: "name2",
      column_label: "label2",
      current: "current2",
      type: 5,
      type_status: "VENDOR_USER",
      type_status_name: "vendor_user",
      vendor_user_id: null,
    },
    {
      id: 3,
      vendor_id: 1,
      column_name: "name3",
      column_label: "label3",
      current: "current3",
      type: 6,
      type_status: "PARTNER_USER",
      type_status_name: "partner_user",
      partner_user_id: null,
    },
    {
      id: 4,
      vendor_id: 1,
      column_name: "name4",
      column_label: "label4",
      current: null,
      type: 4,
      type_status: "DATE",
      type_status_name: "date",
    },
    {
      id: 5,
      vendor_id: 1,
      column_name: "name5",
      column_label: "label5",
      current: "some contents",
      type: 1,
      type_status: "STRING",
      type_status_name: "string",
    },
  ];

  test.each([
    [
      { customColumnId: 1, value: 4 },
      [
        { column_id: 1, select_id: 4 },
        { column_id: 2, vendor_user_id: null },
        { column_id: 3, partner_user_id: null },
        { column_id: 4, custom_column_content: null },
        { column_id: 5, custom_column_content: "some contents" },
      ],
    ],
    [
      { customColumnId: 2, value: 456 },
      [
        { column_id: 1, select_id: 1 },
        { column_id: 2, vendor_user_id: 456 },
        { column_id: 3, partner_user_id: null },
        { column_id: 4, custom_column_content: null },
        { column_id: 5, custom_column_content: "some contents" },
      ],
    ],
    [
      { customColumnId: 3, value: 789 },
      [
        { column_id: 1, select_id: 1 },
        { column_id: 2, vendor_user_id: null },
        { column_id: 3, partner_user_id: 789 },
        { column_id: 4, custom_column_content: null },
        { column_id: 5, custom_column_content: "some contents" },
      ],
    ],
    [
      { customColumnId: 4, value: "2024-01-01" },
      [
        { column_id: 1, select_id: 1 },
        { column_id: 2, vendor_user_id: null },
        { column_id: 3, partner_user_id: null },
        { column_id: 4, custom_column_content: "2024-01-01" },
        { column_id: 5, custom_column_content: "some contents" },
      ],
    ],
    [
      { customColumnId: 5, value: "new contents" },
      [
        { column_id: 1, select_id: 1 },
        { column_id: 2, vendor_user_id: null },
        { column_id: 3, partner_user_id: null },
        { column_id: 4, custom_column_content: null },
        { column_id: 5, custom_column_content: "new contents" },
      ],
    ],
    [
      { customColumnId: 999, value: "no match" },
      [
        { column_id: 1, select_id: 1 },
        { column_id: 2, vendor_user_id: null },
        { column_id: 3, partner_user_id: null },
        { column_id: 4, custom_column_content: null },
        { column_id: 5, custom_column_content: "some contents" },
      ],
    ],
  ])("customColumnContents: %o -> %s", (target, expected) => {
    const result = customColumnContentsMap(
      customColumns as unknown as CustomColumn[],
      target
    );
    expect(result).toEqual(expected);
  });
});
