{"name": "prop-fe", "version": "1.1.0", "private": true, "scripts": {"dev": "next dev -p 6200", "dev_unsafe": "next dev", "build": "next build", "start": "next start", "check": "biome check", "check:fix": "biome check --fix", "storybook": "storybook dev -p 6006", "build-storybook": "storybook build", "vrt:screenshot": "storycap --serverCmd \"npx http-server storybook-static -p 6006 -s publicForStorybook\" http://127.0.0.1:6006 -C stable --captureTimeout 10000 --disableCssAnimation", "vrt:compare": "reg-suit compare", "type-check": "tsc --noEmit", "test": "vitest", "prepare": "npx lefthook install"}, "dependencies": {"@anyflowinc/embed-sdk": "^0.11.0", "@dagrejs/dagre": "^1.1.4", "@emotion/react": "^11.11.1", "@emotion/styled": "^11.10.5", "@fluentui/react-icons": "^2.0.203", "@mantine/core": "^6.0.21", "@mantine/dates": "^6.0.13", "@mantine/dropzone": "^6.0.21", "@mantine/form": "^6.0.13", "@mantine/hooks": "^6.0.21", "@mantine/modals": "^6.0.13", "@mantine/notifications": "^6.0.13", "@mantine/tiptap": "^6.0.22", "@mantine/utils": "^6.0.19", "@radix-ui/react-dialog": "^1.0.2", "@radix-ui/react-icons": "^1.1.1", "@radix-ui/react-select": "^1.1.1", "@radix-ui/react-tabs": "^1.0.1", "@react-pdf-viewer/core": "3.12.0", "@react-pdf-viewer/default-layout": "3.12.0", "@react-pdf-viewer/locales": "^1.0.0", "@remixicon/react": "4.2.0", "@sentry/nextjs": "^8.12.0", "@svgr/webpack": "^6.5.1", "@tabler/icons-react": "^2.47.0", "@tanstack/react-table": "8.20.1", "@tiptap/extension-blockquote": "^2.10.3", "@tiptap/extension-code": "^2.10.3", "@tiptap/extension-code-block": "^2.10.3", "@tiptap/extension-color": "^2.10.3", "@tiptap/extension-drag-handle": "^2.26.1", "@tiptap/extension-file-handler": "^2.26.1", "@tiptap/extension-focus": "^2.10.3", "@tiptap/extension-image": "^2.9.1", "@tiptap/extension-link": "^2.9.1", "@tiptap/extension-mention": "2.11.7", "@tiptap/extension-placeholder": "2.11.7", "@tiptap/extension-text-style": "^2.10.3", "@tiptap/extension-underline": "^2.10.3", "@tiptap/pm": "^2.9.1", "@tiptap/react": "^2.9.1", "@tiptap/starter-kit": "^2.9.1", "@xyflow/react": "^12.3.6", "ag-grid-community": "29.3.5", "ag-grid-react": "29.3.5", "axios": "1.5.1", "dayjs": "^1.11.10", "file-saver": "^2.0.5", "framer-motion": "^7.6.4", "inter-ui": "^3.19.3", "next": "13.5.6", "nookies": "^2.5.2", "pdfjs-dist": "^3.11.174", "pusher": "^5.1.3", "pusher-js": "^8.3.0", "react": "18.2.0", "react-dnd": "^16.0.1", "react-dnd-html5-backend": "^16.0.1", "react-dom": "18.2.0", "react-hook-form": "^7.39.1", "react-hot-toast": "^2.4.0", "react-intersection-observer": "^9.13.1", "react-pdf": "^7.3.3", "react-resizable-panels": "^2.1.7", "recoil": "^0.7.6", "ress": "^5.0.2", "swr": "^2.2.0", "tippy.js": "6.3.7", "yup": "^1.4.0"}, "devDependencies": {"@biomejs/biome": "2.0.0-beta.4", "@emotion/babel-preset-css-prop": "11.11.0", "@storybook/addon-essentials": "^8.3.2", "@storybook/addon-links": "^8.3.2", "@storybook/nextjs": "^8.3.2", "@storybook/react": "^8.3.2", "@testing-library/dom": "10.4.0", "@testing-library/react": "16.0.1", "@types/file-saver": "^2.0.5", "@types/node": "18.11.9", "@types/pusher-js": "^5.1.0", "@types/react": "18.0.24", "@types/react-dom": "18.0.8", "@vitejs/plugin-react": "4.3.1", "http-server": "14.1.1", "jsdom": "25.0.0", "lefthook": "1.8.1", "local-ssl-proxy": "^2.0.5", "raw-loader": "^4.0.2", "reg-suit": "0.14.4", "storybook": "^8.3.2", "storycap": "5.0.1", "typescript": "5.6.3", "vite-tsconfig-paths": "5.0.1", "vitest": "2.1.1"}, "volta": {"node": "18.20.4"}}