# 🔧 Edit Page Link Feature

## ✅ **Tính năng đã hoàn thành:**

### **"ページリンクを変更" Action**
Khi click vào "ページリンクを変更" trong action menu:

1. **Xóa page link hiện tại** 
2. **Mở lại PageLinkDropdown** để chọn page link mới
3. **<PERSON>i<PERSON><PERSON> hệt như click "ページリンクを挿入" ban đầu**

## 🎯 **User Flow:**

```
1. User double-click page link
   ↓
2. Action menu hiển thị
   ↓  
3. User click "ページリンクを変更"
   ↓
4. Page link hiện tại bị xóa
   ↓
5. PageLinkDropdown mở ra
   ↓
6. User chọn page link mới
   ↓
7. Page link mới được chèn vào
```

## 🔧 **Technical Implementation:**

### **1. Custom Event Communication:**
```typescript
// PageLinkNodeView - Dispatch event
const handleEdit = () => {
  setShowActionMenu(false);
  
  // Delete current page link
  deleteNode();
  
  // Dispatch custom event to trigger d-block dropdown
  const event = new CustomEvent('openPageLinkDropdown', {
    detail: {
      position: menuPosition, // Use same position as action menu
    }
  });
  window.dispatchEvent(event);
};
```

### **2. Event Listener in D-Block:**
```typescript
// d-block-node-view.tsx - Listen for event
useEffect(() => {
  const handleOpenPageLinkDropdown = (event: CustomEvent) => {
    const { position } = event.detail;
    setDropdownPosition(position);
    setShowPageLinkDropdown(true);
    setShowPlaceholder(true);
    setShowMenu(false);
  };

  window.addEventListener('openPageLinkDropdown', handleOpenPageLinkDropdown);
  
  return () => {
    window.removeEventListener('openPageLinkDropdown', handleOpenPageLinkDropdown);
  };
}, []);
```

## 🎨 **UI/UX Flow:**

### **Before Edit:**
```
📄 [チラシ] これ1枚でわかる「PartnerProp」.pdf
```

### **Double-click → Action Menu:**
```
┌─────────────────────┐
│ 🔧 ページリンクを変更  │ ← Click này
├─────────────────────┤
│ 📋 複製             │
├─────────────────────┤  
│ 🗑️ 削除             │
└─────────────────────┘
```

### **After Click "ページリンクを変更":**
```
┌─────────────────────────────────┐
│ URLを貼り付けまたは検索           │
├─────────────────────────────────┤
│ 📄 4323423423                   │
│ 📄 PartnerProp導入事例.pdf       │
│ 📄 営業資料_2024年度版.docx      │
│ 📄 プロジェクト企画書.pptx       │
└─────────────────────────────────┘
```

## 🔄 **Component Communication:**

```
PageLinkNodeView ──────► D-BlockNodeView
     │                       │
     │ Custom Event          │
     │ 'openPageLinkDropdown' │
     │                       │
     ▼                       ▼
deleteNode()           setShowPageLinkDropdown(true)
```

## 📋 **Event Detail Structure:**
```typescript
interface OpenPageLinkDropdownEvent {
  detail: {
    position: {
      top: number;
      left: number;
    }
  }
}
```

## ✅ **Advantages của approach này:**

### **1. Loose Coupling:**
- PageLinkNodeView không cần biết về D-Block implementation
- D-Block không cần expose methods
- Clean separation of concerns

### **2. Reusable:**
- Có thể trigger từ bất kỳ component nào
- Event-driven architecture
- Easy to extend

### **3. Position Consistency:**
- Dropdown hiển thị ở cùng vị trí với action menu
- Smooth user experience
- No jarring position jumps

## 🧪 **Test Cases:**

### ✅ **Happy Path:**
1. Double-click page link → Action menu hiển thị
2. Click "ページリンクを変更" → Page link bị xóa
3. PageLinkDropdown mở ra → User có thể chọn file mới
4. Chọn file → Page link mới được chèn

### ✅ **Edge Cases:**
1. **Multiple page links**: Chỉ affect page link được click
2. **Event cleanup**: Proper removeEventListener on unmount
3. **Position calculation**: Dropdown hiển thị đúng vị trí

## 🚀 **Next Steps (Optional):**

1. **Animation**: Smooth transition khi xóa và mở dropdown
2. **Keyboard shortcuts**: Ctrl+E để edit page link
3. **Undo/Redo**: Support undo cho edit action
4. **Batch edit**: Edit multiple page links cùng lúc

## 📝 **Files Modified:**

1. **`page-link-node-view.tsx`**:
   - Updated `handleEdit()` function
   - Added custom event dispatch
   - Added `deleteNode()` call

2. **`d-block-node-view.tsx`**:
   - Added event listener for 'openPageLinkDropdown'
   - Added cleanup in useEffect
   - Reused existing dropdown logic

## 🎉 **Result:**

User có thể **edit page link một cách intuitive**:
- Double-click → Action menu
- Click "ページリンクを変更" → Dropdown mở ra
- Chọn file mới → Done!

**Seamless editing experience** giống như tạo page link mới! 🚀
