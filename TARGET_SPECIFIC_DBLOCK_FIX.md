# 🎯 Target Specific D-Block Fix

## ❌ **Vấn đề trước đây:**
<PERSON><PERSON> click "ページリンクを変更" trong action menu:
- **Tất cả d-block components** đều hiển thị "ページリンクを挿入"
- **Global event** được broadcast đến mọi d-block
- **User experience rối loạn** với nhiều dropdown cùng lúc

## ✅ **Giải pháp đã implement:**

### **1. Targeted Event Dispatch:**
```typescript
// PageLinkNodeView - Find specific d-block parent
const handleEdit = () => {
  setShowActionMenu(false);
  
  // Find the parent d-block element
  const pageLink = nodeRef.current;
  if (!pageLink) return;
  
  // Find the d-block container
  let dBlockElement = pageLink.closest('[data-type="d-block"]');
  if (!dBlockElement) {
    dBlockElement = pageLink.closest('.d-block') || 
                   pageLink.closest('[data-node-type="d-block"]');
  }
  
  if (dBlockElement) {
    deleteNode();
    
    // Dispatch event specifically to THIS d-block only
    const event = new CustomEvent('openPageLinkDropdown', {
      detail: {
        position: menuPosition,
        targetElement: dBlockElement, // Include target element
      },
      bubbles: false, // Don't bubble to prevent affecting other d-blocks
    });
    dBlockElement.dispatchEvent(event); // Dispatch to specific element
  }
};
```

### **2. Element-Specific Event Listener:**
```typescript
// d-block-node-view.tsx - Listen only on this element
useEffect(() => {
  const handleOpenPageLinkDropdown = (event: CustomEvent) => {
    const { position, targetElement } = event.detail;
    
    // Only handle if this d-block is the target
    if (targetElement && nodeRef.current && targetElement === nodeRef.current) {
      setDropdownPosition(position);
      setShowPageLinkDropdown(true);
      setShowPlaceholder(true);
      setShowMenu(false);
    }
  };

  // Listen for the event on this specific d-block element
  if (nodeRef.current) {
    nodeRef.current.addEventListener('openPageLinkDropdown', handleOpenPageLinkDropdown);
  }
  
  return () => {
    if (nodeRef.current) {
      nodeRef.current.removeEventListener('openPageLinkDropdown', handleOpenPageLinkDropdown);
    }
  };
}, [nodeRef.current]);
```

## 🎯 **Key Changes:**

### **Before (Global Event):**
```
PageLink → window.dispatchEvent() → ALL d-blocks receive event
```

### **After (Targeted Event):**
```
PageLink → Find parent d-block → dBlockElement.dispatchEvent() → ONLY target d-block receives event
```

## 🔧 **Technical Details:**

### **1. DOM Traversal:**
- `pageLink.closest('[data-type="d-block"]')` - Primary selector
- `pageLink.closest('.d-block')` - Fallback class selector  
- `pageLink.closest('[data-node-type="d-block"]')` - Alternative attribute

### **2. Event Configuration:**
- `bubbles: false` - Prevent event from bubbling up
- `targetElement` in detail - Include target for verification
- Element-specific dispatch - Not global window event

### **3. Target Verification:**
- Check `targetElement === nodeRef.current`
- Only process event if this d-block is the target
- Ignore events meant for other d-blocks

## 🎨 **User Experience:**

### **Before Fix:**
```
Document with multiple d-blocks:
┌─────────────────────┐
│ ページリンクを挿入    │ ← All show this
├─────────────────────┤
│ ページリンクを挿入    │ ← All show this  
├─────────────────────┤
│ ページリンクを挿入    │ ← All show this
└─────────────────────┘
```

### **After Fix:**
```
Document with multiple d-blocks:
┌─────────────────────┐
│ Normal content      │ ← Normal
├─────────────────────┤
│ [Dropdown here]     │ ← Only target shows dropdown
├─────────────────────┤  
│ Normal content      │ ← Normal
└─────────────────────┘
```

## ✅ **Benefits:**

### **1. Precise Targeting:**
- Chỉ d-block chứa page link được edit mới hiển thị dropdown
- Không affect các d-block khác
- Clean, focused user experience

### **2. Better Performance:**
- Không có unnecessary re-renders
- Event chỉ được process bởi target element
- Reduced DOM manipulation

### **3. Maintainable Code:**
- Clear parent-child relationship
- Explicit targeting logic
- Easy to debug and extend

## 🧪 **Test Cases:**

### ✅ **Single D-Block:**
1. Page link trong 1 d-block
2. Double-click → Action menu
3. Click "ページリンクを変更" → Chỉ d-block đó hiển thị dropdown

### ✅ **Multiple D-Blocks:**
1. Document có nhiều d-blocks với page links
2. Edit page link trong d-block thứ 2
3. Chỉ d-block thứ 2 hiển thị dropdown
4. Các d-block khác không bị affect

### ✅ **Edge Cases:**
1. **Nested elements**: DOM traversal tìm đúng parent d-block
2. **Missing d-block**: Fallback gracefully với multiple selectors
3. **Event cleanup**: Proper removeEventListener on unmount

## 📝 **Files Modified:**

### **1. `page-link-node-view.tsx`:**
- Added DOM traversal to find parent d-block
- Changed from `window.dispatchEvent()` to `element.dispatchEvent()`
- Added `targetElement` in event detail
- Added fallback selectors for d-block detection

### **2. `d-block-node-view.tsx`:**
- Changed from `window.addEventListener()` to `element.addEventListener()`
- Added target verification logic
- Updated useEffect dependencies
- Added proper cleanup for element-specific listeners

## 🎉 **Result:**

**Perfect targeted behavior:**
- ✅ Chỉ d-block chứa page link được edit mới hiển thị dropdown
- ✅ Các d-block khác hoàn toàn không bị ảnh hưởng  
- ✅ Clean, intuitive user experience
- ✅ No more "ページリンクを挿入" spam across all d-blocks

**Seamless edit experience** như mong muốn! 🚀
